#include "key_app.h"
#include "system.h"

void Key_Init(void)
{
	SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOC);
	SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOJ);
	GPIODirModeSet(GP<PERSON>_PORTC_BASE, GPIO_PIN_2, GPIO_DIR_MODE_IN); // SW1
	GPIOPadConfigSet(GPIO_PORTC_BASE, GPIO_PIN_2, GP<PERSON>_STRENGTH_2MA, GPIO_PIN_TYPE_STD_WPU);
	GPIODirModeSet(GPIO_PORTJ_BASE, GPIO_PIN_0, GPIO_DIR_MODE_IN); // SW2
	GPIOPadConfigSet(GPIO_PORTJ_BASE, GPIO_PIN_0, GPIO_STRENGTH_2MA, GPIO_PIN_TYPE_STD_WPU);
	// ����������
	GPIODirModeSet(GPIO_PORTJ_BASE, GPIO_PIN_2, GPIO_DIR_MODE_OUT); // BEEP
	GPIOPadConfigSet(GPIO_PORTJ_BASE, GPIO_PIN_2, GPIO_STRENGTH_2MA, GPIO_PIN_TYPE_STD);
}

uint8_t Key_Read(void)
{
	uint8_t temp = 0;
	if(GPIOPinRead(GPIO_PORTC_BASE , GPIO_PIN_2) == 0) temp = 1;
	if(GPIOPinRead(GPIO_PORTJ_BASE , GPIO_PIN_0) == 0) temp = 2;
	return temp;
}

uint8_t Key_Down,Key_Up,Key_Val,Key_Old;

void Key_Proc()
{
	Key_Val = Key_Read();
	Key_Down = Key_Val & (Key_Val ^ Key_Old);
	Key_Up = ~Key_Val & (Key_Val ^ Key_Old);
	Key_Old = Key_Val;
	
	switch(Key_Down)
	{
		case 1:
			GPIOPinWrite(GPIO_PORTJ_BASE,GPIO_PIN_2,GPIO_PIN_2);
			printf("key_1 down\r\n");
		break;
		case 2:
			GPIOPinWrite(GPIO_PORTJ_BASE,GPIO_PIN_2,0);
			printf("key_2 down\r\n");
		break;
	}
}


