#ifndef __OLED_APP_H
#define __OLED_APP_H

#include <stdint.h>

// �����кź꣨ÿ��8���ظߣ�
#define LINE0 0
#define LINE1 8
#define LINE2 16
#define LINE3 24
#define LINE4 32
#define LINE5 40
#define LINE6 48
#define LINE7 56

// ��������
void Init(void);
void LcdSprintf(unsigned char Line, char *format, ...);
void LCD_DisplayStringLine(uint8_t y, char *str);
uint8_t u8g2_gpio_and_delay(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);
void spi_send_byte(uint8_t data);
uint8_t u8x8_byte_4wire_sw_spi(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);
void Oled_Proc();

#endif /* __OLED_APP_H */

