#include "system.h"

void usart_send(float roll, float pitch, float yaw, uint8_t fusion_sta)
{
    uint8_t buffer[15];  // ����֡������
    uint8_t sumcheck = 0; // У��ͱ���
    uint8_t addcheck = 0; // ����У�����
    uint8_t index = 0;    // ����������

    // ��ŷ����ת��Ϊ int16�����Ŵ�100��
    int16_t roll_int = (int16_t)(roll * 100.0f);
    int16_t pitch_int = (int16_t)(pitch * 100.0f);
    int16_t yaw_int = (int16_t)(yaw * 100.0f);

    // ֡ͷ (0xAB)
    buffer[index++] = 0xAB;
    // Դ��ַ (����Ϊ 0xDC, �����ɿص�Ĭ�ϵ�ַ)
    buffer[index++] = 0xDC;
    // Ŀ���ַ (0xFE, ��λ����ַ)
    buffer[index++] = 0xFE;
    // ������ (ID: 0x03 ��ʾ�ɿ���̬��ŷ���Ǹ�ʽ)
    buffer[index++] = 0x03;
    // ���ݳ��� (7�ֽ�����)
    buffer[index++] = 7;
    buffer[index++] = 0;  // ���ݳ��ȸ��ֽ�Ϊ0

    // ŷ�������� (int16, �Ƕ�����100��)
    buffer[index++] = (uint8_t)(roll_int & 0xFF);
    buffer[index++] = (uint8_t)((roll_int >> 8) & 0xFF);
    buffer[index++] = (uint8_t)(pitch_int & 0xFF);
    buffer[index++] = (uint8_t)((pitch_int >> 8) & 0xFF);
    buffer[index++] = (uint8_t)(yaw_int & 0xFF);
    buffer[index++] = (uint8_t)((yaw_int >> 8) & 0xFF);

    // �ں�״̬ (uint8)
    buffer[index++] = fusion_sta;

    // ����У��ͺ͸���У�� (��֡ͷ��ʼ��DATA������)
    for (int i = 0; i < index; i++)
    {
        sumcheck += buffer[i];
        addcheck += sumcheck;
    }

    // ���У��ͺ͸���У��ֵ
    buffer[index++] = sumcheck;
    buffer[index++] = addcheck;

    // ��������֡
    for (int i = 0; i < index; i++)
    {
        printf("%c", buffer[i]);
    }
}

u8g2_t u8g2;


uint8_t counter = 0;

// ��UI���ƺ��� ������
void draw_simple_ui(u8g2_t *u8g2) 
{
    char buf[20];
    
    // ���������
    u8g2_ClearBuffer(u8g2);
    
    // ��������
    u8g2_SetFont(u8g2, u8g2_font_profont12_mf);
    
    // ���Ʊ���
    u8g2_DrawStr(u8g2, 0, 12, "U8G2");
    
    // ���Ʒָ���
    u8g2_DrawHLine(u8g2, 0, 14, 128);
    
    // ��ʾ������ֵ
    sprintf(buf, "count: %d", counter);
    u8g2_DrawStr(u8g2, 0, 30, buf);
    
    // ����һ���򵥵Ľ�����
    u8g2_DrawFrame(u8g2, 0, 40, 100, 10);
    u8g2_DrawBox(u8g2, 0, 40, counter % 101, 10);
    
    // ���ͻ���������ʾ��
    u8g2_SendBuffer(u8g2);
		
		counter++;
}

void draw_geometry_demo(u8g2_t *u8g2)
{
    // 完全清空缓冲区，确保没有残留数据
    u8g2_ClearBuffer(u8g2);

    // 绘制简单几何图形
    u8g2_DrawFrame(u8g2, 10, 10, 100, 40);      // 矩形框

    // 添加一些文本以验证显示正常
    u8g2_SetFont(u8g2, u8g2_font_profont12_mf);
    u8g2_DrawStr(u8g2, 15, 25, "U8G2 Test");
    u8g2_DrawStr(u8g2, 15, 35, "Display OK");

    // 在底部绘制一条线，确保整个屏幕区域都被正确管理
    u8g2_DrawHLine(u8g2, 0, 60, 128);

    // 发送缓冲区到显示器
    u8g2_SendBuffer(u8g2);
}

int main(void)
{
	ROM_FPUEnable();//ʹ�ܸ��㵥Ԫ
	ROM_FPULazyStackingEnable();//�����ӳٶ�ջ,�����ж���Ӧ�ӳ� 
	ROM_SysCtlClockSet(SYSCTL_SYSDIV_2_5 | SYSCTL_USE_PLL | SYSCTL_XTAL_16MHZ |SYSCTL_OSC_MAIN);//����ϵͳʱ��
	ROM_SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOF);
	ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_5);//��ɫ
	ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_6);//��ɫ
	ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_4);//��ɫ
	initTime();
	ConfigureUART();//��ʼ������0
	ConfigureDMA();
	ADC_DMA_Init();
	Key_Init();
	Init_I2C();																					 
	ICM20608_Init();																			
	Time_init();

	// 先进行GPIO硬件初始化（必需，u8g2库依赖外部GPIO配置）
	OLED_Hardware_Init_Only();

	// 然后使用u8g2库进行显示初始化
	u8g2_Setup_ssd1306_128x64_noname_f(&u8g2, U8G2_R0, u8x8_byte_4wire_sw_spi, u8g2_gpio_and_delay);
	u8g2_InitDisplay(&u8g2);
	u8g2_SetPowerSave(&u8g2, 0);

	// 彻底清理显示控制器和缓冲区
	u8g2_ClearDisplay(&u8g2);  // 清理显示控制器RAM
	u8g2_ClearBuffer(&u8g2);   // 清理u8g2缓冲区
	u8g2_SendBuffer(&u8g2);    // 发送空缓冲区到显示器
	delay_ms(50);              // 等待显示控制器处理

	// 再次清理确保彻底
	u8g2_ClearBuffer(&u8g2);
	u8g2_SendBuffer(&u8g2);
	delay_ms(50);
	scheduler_init();
	while(1)
	{
//		scheduler_run();
//		draw_simple_ui(&u8g2);
		draw_geometry_demo(&u8g2);
		delay_ms(500);
	}
}
