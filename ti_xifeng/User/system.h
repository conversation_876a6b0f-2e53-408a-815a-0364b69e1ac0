#include "stdio.h"
#include "stdint.h"
#include "stdbool.h"
#include "hw_memmap.h"
#include "hw_types.h"
#include "hw_gpio.h"
#include "debug.h"
#include "fpu.h"
#include "gpio.h"
#include "pin_map.h"
#include "rom.h"
#include "sysctl.h"
#include "uart.h"
#include "uartstdio.h"
#include "interrupt.h"
#include "hw_ints.h"
#include "timer.h"
#include "string.h"
#include "udma.h"
#include "hw_uart.h"
#include "hw_adc.h"
#include "adc.h"
#include "SystickTime.h"
#include "oled.h"


#include "scheduler.h"
#include "uart_app.h"
#include "key_app.h"
#include "adc_app.h"
#include "oled_app.h"
#include "icm20608.h"
#include "myiic.h"
#include "u8g2.h"
#include "imu.h"


extern uint32_t systick;
extern Sensor WP_Sensor;

extern uint8_t u8g2_gpio_and_delay(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);
extern uint8_t u8x8_byte_4wire_sw_spi(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);
extern void delay_us(uint32_t us);