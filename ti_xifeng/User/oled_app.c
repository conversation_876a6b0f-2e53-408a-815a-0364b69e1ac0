#include "oled_app.h"
#include "system.h"
#include "u8g2.h"
// #include "ssd1306.h"  // 暂时禁用ssd1306库以避免冲突

void Init(void)
{
	// 暂时禁用ssd1306库的调用，统一使用u8g2库
	// ssd1306_clear_display();
	// ssd1306_set_rotation(0);
	// ssd1306_set_textsize(1);
	// ssd1306_set_textcolor(WHITE);
}

// LcdSprintf ����ʵ��
void LcdSprintf(unsigned char Line, char *format, ...)
{
	char String[21];          // ������������ÿ�����20���ַ�
	va_list arg;
	va_start(arg, format);
	vsprintf(String, format, arg);
	va_end(arg);
	LCD_DisplayStringLine(Line, String);
}

// 显示字符串到指定行的函数 - 暂时禁用以避免库冲突
void LCD_DisplayStringLine(uint8_t y, char *str)
{
	// 暂时禁用ssd1306库的调用，避免与u8g2库冲突
	// 如需使用文本显示，请在main.c中使用u8g2库的函数
	/*
	uint8_t x = 0;
	ssd1306_set_textsize(1);
	ssd1306_set_textcolor(WHITE);
	ssd1306_set_cursor(0, y);
	ssd1306_puts(str);
	ssd1306_display();
	*/
}

// 注意：u8g2_gpio_and_delay函数已在u8x8_byte.c中实现，此处不再重复定义

// ���SPI�����ֽں���
void spi_send_byte(uint8_t data)
{
    for(int i = 7; i >= 0; i--)
    {
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, 0);
        delay_us(1);
        
        // ��������λ
        if(data & (1 << i))
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, GPIO_PIN_4); // SDA��
        else
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, 0);          // SDA��
        
        delay_us(1);
        
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, GPIO_PIN_5);
        delay_us(1);
    }
}

// 注意：u8x8_byte_4wire_sw_spi函数已在u8x8_byte.c中实现，此处不再重复定义

void Oled_Proc()
{
	// ��ʾһ������
	LcdSprintf(LINE0, "Value: %d", 123);

	// ��ʾһ��������
	LcdSprintf(LINE1, "Temp: %.1f C", 36.5);

	// ��ʾһ���ַ���
	LcdSprintf(LINE2, "Status: %s", "OK");
}


