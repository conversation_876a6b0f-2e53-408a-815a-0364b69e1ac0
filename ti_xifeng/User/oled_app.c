#include "oled_app.h"
#include "system.h"
#include "u8g2.h"
#include "ssd1306.h" 

void Init(void)
{
	ssd1306_clear_display();

	ssd1306_set_rotation(0);

	ssd1306_set_textsize(1);
	ssd1306_set_textcolor(WHITE);
}

// LcdSprintf ����ʵ��
void LcdSprintf(unsigned char Line, char *format, ...)
{
	char String[21];          // ������������ÿ�����20���ַ�
	va_list arg;
	va_start(arg, format);
	vsprintf(String, format, arg);
	va_end(arg);
	LCD_DisplayStringLine(Line, String);
}

// ��ʾ�ַ�����ָ���еĺ���
void LCD_DisplayStringLine(uint8_t y, char *str)
{
	uint8_t x = 0;
	
	// �����ı���СΪ1��6x8�������壩
	ssd1306_set_textsize(1);
	
	// �����ı���ɫΪ��ɫ
	ssd1306_set_textcolor(WHITE);
	
	// ���ù��λ��
	ssd1306_set_cursor(0, y);
	
	// ����ַ���
	ssd1306_puts(str);
	
	// ˢ����ʾ
	ssd1306_display();
}

// 注意：u8g2_gpio_and_delay函数已在u8x8_byte.c中实现，此处不再重复定义

// ���SPI�����ֽں���
void spi_send_byte(uint8_t data)
{
    for(int i = 7; i >= 0; i--)
    {
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, 0);
        delay_us(1);
        
        // ��������λ
        if(data & (1 << i))
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, GPIO_PIN_4); // SDA��
        else
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, 0);          // SDA��
        
        delay_us(1);
        
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, GPIO_PIN_5);
        delay_us(1);
    }
}

// 注意：u8x8_byte_4wire_sw_spi函数已在u8x8_byte.c中实现，此处不再重复定义

void Oled_Proc()
{
	// ��ʾһ������
	LcdSprintf(LINE0, "Value: %d", 123);

	// ��ʾһ��������
	LcdSprintf(LINE1, "Temp: %.1f C", 36.5);

	// ��ʾһ���ַ���
	LcdSprintf(LINE2, "Status: %s", "OK");
}


