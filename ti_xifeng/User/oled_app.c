#include "oled_app.h"
#include "system.h"
#include "u8g2.h"
#include "ssd1306.h" 

void Init(void)
{
	ssd1306_clear_display();

	ssd1306_set_rotation(0);

	ssd1306_set_textsize(1);
	ssd1306_set_textcolor(WHITE);
}

// LcdSprintf ����ʵ��
void LcdSprintf(unsigned char Line, char *format, ...)
{
	char String[21];          // ������������ÿ�����20���ַ�
	va_list arg;
	va_start(arg, format);
	vsprintf(String, format, arg);
	va_end(arg);
	LCD_DisplayStringLine(Line, String);
}

// ��ʾ�ַ�����ָ���еĺ���
void LCD_DisplayStringLine(uint8_t y, char *str)
{
	uint8_t x = 0;
	
	// �����ı���СΪ1��6x8�������壩
	ssd1306_set_textsize(1);
	
	// �����ı���ɫΪ��ɫ
	ssd1306_set_textcolor(WHITE);
	
	// ���ù��λ��
	ssd1306_set_cursor(0, y);
	
	// ����ַ���
	ssd1306_puts(str);
	
	// ˢ����ʾ
	ssd1306_display();
}

// GPIO����ʱ�ص�����
uint8_t u8g2_gpio_and_delay(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
    switch(msg)
    {
        case U8X8_MSG_GPIO_AND_DELAY_INIT:
            // GPIO��ʼ�����������ط����
            break;
            
        case U8X8_MSG_DELAY_MILLI:
            delay_ms(arg_int);
            break;
            
        case U8X8_MSG_DELAY_10MICRO:
            delay_us(arg_int * 10);
            break;
            
        case U8X8_MSG_DELAY_100NANO:
            delay_us(1); // ��С��ʱ
            break;
            
        case U8X8_MSG_GPIO_DC:
            // DC���ſ��� (��������Ӳ�������޸�)
            if (arg_int) 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_2, GPIO_PIN_2);
            else 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_2, 0);
            break;
            
        case U8X8_MSG_GPIO_RESET:
            // ��λ���ſ���
            if (arg_int) 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_3, GPIO_PIN_3);
            else 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_3, 0);
            break;
            
        default:
            return 0;
    }
    return 1;
}

// ���SPI�����ֽں���
void spi_send_byte(uint8_t data)
{
    for(int i = 7; i >= 0; i--)
    {
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, 0);
        delay_us(1);
        
        // ��������λ
        if(data & (1 << i))
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, GPIO_PIN_4); // SDA��
        else
            GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_4, 0);          // SDA��
        
        delay_us(1);
        
        // SCL��
        GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_5, GPIO_PIN_5);
        delay_us(1);
    }
}

// SPI�ֽڴ���ص�����
uint8_t u8x8_byte_4wire_sw_spi(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
    uint8_t *data;
    
    switch(msg)
    {
        case U8X8_MSG_BYTE_INIT:
            // SPI��ʼ��
            break;
            
        case U8X8_MSG_BYTE_SET_DC:
            // ����DC����
            if (arg_int) 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_2, GPIO_PIN_2);
            else 
                GPIOPinWrite(GPIO_PORTB_BASE, GPIO_PIN_2, 0);
            break;
            
        case U8X8_MSG_BYTE_START_TRANSFER:
            break;
            
        case U8X8_MSG_BYTE_SEND:
            data = (uint8_t *)arg_ptr;
            while(arg_int > 0)
            {
                spi_send_byte(*data);  // ����һ���ֽ�
                data++;
                arg_int--;
            }
            break;
            
        case U8X8_MSG_BYTE_END_TRANSFER:
            break;
            
        default:
            return 0;
    }
    return 1;
}

void Oled_Proc()
{
	// ��ʾһ������
	LcdSprintf(LINE0, "Value: %d", 123);

	// ��ʾһ��������
	LcdSprintf(LINE1, "Temp: %.1f C", 36.5);

	// ��ʾһ���ַ���
	LcdSprintf(LINE2, "Status: %s", "OK");
}


