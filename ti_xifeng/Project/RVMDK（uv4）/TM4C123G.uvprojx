<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>TIVA</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>TM4C123GH6PZ</Device>
          <Vendor>Texas Instruments</Vendor>
          <PackID>Keil.TM4C_DFP.1.1.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x008000) IROM(0x00000000,0x040000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0TM4C123_256 -FS00 -FL040000 -FP0($$Device:TM4C123GH6PZ$Flash\TM4C123_256.FLM))</FlashDriverDll>
          <DeviceId>6019</DeviceId>
          <RegisterFile>$$Device:TM4C123GH6PZ$Device\Include\TM4C123\TM4C123.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc>-DTM4C123GH6PZ</SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:TM4C123GH6PZ$SVD\TM4C123\TM4C123GH6PZ.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\Output\</OutputDirectory>
          <OutputName>TM4C123G</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>-MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--c99</MiscControls>
              <Define>rvmdk PART_TM4C123GH6PZ TARGET_IS_BLIZZARD_RB1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Libraries\inc;..\..\Libraries\utils;..\..\Libraries\driverlib;..\..\User;..\..\User\u8g2</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\adc.c</FilePath>
            </File>
            <File>
              <FileName>aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\aes.c</FilePath>
            </File>
            <File>
              <FileName>can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\can.c</FilePath>
            </File>
            <File>
              <FileName>comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\comp.c</FilePath>
            </File>
            <File>
              <FileName>cpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\cpu.c</FilePath>
            </File>
            <File>
              <FileName>crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\crc.c</FilePath>
            </File>
            <File>
              <FileName>des.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\des.c</FilePath>
            </File>
            <File>
              <FileName>eeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\eeprom.c</FilePath>
            </File>
            <File>
              <FileName>emac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\emac.c</FilePath>
            </File>
            <File>
              <FileName>epi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\epi.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\flash.c</FilePath>
            </File>
            <File>
              <FileName>fpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\fpu.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\gpio.c</FilePath>
            </File>
            <File>
              <FileName>hibernate.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\hibernate.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\i2c.c</FilePath>
            </File>
            <File>
              <FileName>interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\interrupt.c</FilePath>
            </File>
            <File>
              <FileName>lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\lcd.c</FilePath>
            </File>
            <File>
              <FileName>mpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\mpu.c</FilePath>
            </File>
            <File>
              <FileName>pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\pwm.c</FilePath>
            </File>
            <File>
              <FileName>qei.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\qei.c</FilePath>
            </File>
            <File>
              <FileName>shamd5.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\shamd5.c</FilePath>
            </File>
            <File>
              <FileName>ssi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\ssi.c</FilePath>
            </File>
            <File>
              <FileName>sw_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sw_crc.c</FilePath>
            </File>
            <File>
              <FileName>sysctl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sysctl.c</FilePath>
            </File>
            <File>
              <FileName>sysexc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sysexc.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\systick.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\timer.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\uart.c</FilePath>
            </File>
            <File>
              <FileName>udma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\udma.c</FilePath>
            </File>
            <File>
              <FileName>usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\usb.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\watchdog.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>uartstdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\utils\uartstdio.c</FilePath>
            </File>
            <File>
              <FileName>startup_rvmdk.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\User\startup_rvmdk.S</FilePath>
            </File>
            <File>
              <FileName>uart_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\uart_app.c</FilePath>
            </File>
            <File>
              <FileName>system.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\system.h</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>key_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\key_app.c</FilePath>
            </File>
            <File>
              <FileName>adc_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\adc_app.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\myiic.c</FilePath>
            </File>
            <File>
              <FileName>icm20608.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\icm20608.c</FilePath>
            </File>
            <File>
              <FileName>WP_DataType.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\WP_DataType.h</FilePath>
            </File>
            <File>
              <FileName>SystickTime.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\SystickTime.c</FilePath>
            </File>
            <File>
              <FileName>imu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\imu.c</FilePath>
            </File>
            <File>
              <FileName>glcdfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\glcdfont.c</FilePath>
            </File>
            <File>
              <FileName>OLED.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\OLED.c</FilePath>
            </File>
            <File>
              <FileName>ssd1306.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\ssd1306.c</FilePath>
            </File>
            <File>
              <FileName>oled_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\oled_app.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Doc\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>U8G2</GroupName>
          <Files>
            <File>
              <FileName>mui.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\mui.c</FilePath>
            </File>
            <File>
              <FileName>mui_u8g2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\mui_u8g2.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_bitmap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_bitmap.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_box.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_box.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_buffer.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_button.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_button.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_circle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_circle.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_cleardisplay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_cleardisplay.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_d_memory.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_d_memory.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_d_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_d_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_font.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_fonts.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_fonts.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_hvline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_hvline.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_input_value.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_input_value.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_intersection.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_intersection.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_kerning.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_kerning.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_line.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_ll_hvline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_ll_hvline.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_message.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_message.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_polygon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_polygon.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_selection_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_selection_list.c</FilePath>
            </File>
            <File>
              <FileName>u8g2_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8g2_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8log.c</FilePath>
            </File>
            <File>
              <FileName>u8log_u8g2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8log_u8g2.c</FilePath>
            </File>
            <File>
              <FileName>u8log_u8x8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8log_u8x8.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_8x8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_8x8.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_byte.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_byte.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_cad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_cad.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_capture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_capture.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_d_ssd1306_128x64_noname.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_d_ssd1306_128x64_noname.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_debounce.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_debounce.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_display.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_fonts.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_fonts.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_gpio.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_input_value.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_input_value.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_message.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_message.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_selection_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_selection_list.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_setup.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_string.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_string.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_u8toa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_u8toa.c</FilePath>
            </File>
            <File>
              <FileName>u8x8_u16toa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\u8g2\u8x8_u16toa.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.4.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.7.0"/>
        <targetInfos>
          <targetInfo name="TIVA"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>
