<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\..\Output\TM4C123G.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\..\Output\TM4C123G.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Wed Jul 30 23:36:06 2025
<BR><P>
<H3>Maximum Stack Usage =        304 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; draw_geometry_demo &rArr; u8g2_DrawStr &rArr; u8g2_draw_string &rArr; u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[2]">Reset_Handler</a>
 <LI><a href="#[3]">NmiSR</a>
 <LI><a href="#[4]">FaultISR</a>
 <LI><a href="#[5]">IntDefaultHandler</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[27]">SysCtlDelay</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[27]">SysCtlDelay</a><BR>
 <LI><a href="#[3]">NmiSR</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">NmiSR</a><BR>
 <LI><a href="#[4]">FaultISR</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">FaultISR</a><BR>
 <LI><a href="#[5]">IntDefaultHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">IntDefaultHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[bd]">ADC0_Handler</a> from adc_app.o(.text) referenced from adc_app.o(.text)
 <LI><a href="#[4]">FaultISR</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[5]">IntDefaultHandler</a> from startup_rvmdk.o(RESET) referenced 115 times from startup_rvmdk.o(RESET)
 <LI><a href="#[3]">NmiSR</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[1]">Oled_Proc</a> from oled_app.o(.text) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[2]">Reset_Handler</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[a]">SycTickHandler</a> from systicktime.o(.text) referenced from systicktime.o(.text)
 <LI><a href="#[ab]">TIMER0A_Handler</a> from uart_app.o(.text) referenced from uart_app.o(.text)
 <LI><a href="#[98]">UART0_IRQHandler</a> from uart_app.o(.text) referenced from uart_app.o(.text)
 <LI><a href="#[0]">Uart_Proc</a> from uart_app.o(.text) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7]">_IntDefaultHandler</a> from interrupt.o(.text) referenced from interrupt.o(.text)
 <LI><a href="#[15]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[15]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0vsprintf)
 <LI><a href="#[14]">fputc</a> from uart_app.o(.text) referenced from printfa.o(i.__0printf)
 <LI><a href="#[6]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[22]">u8g2_draw_l90_mirrorr_r0</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[18]">u8g2_draw_l90_r0</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[1b]">u8g2_draw_l90_r1</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[1e]">u8g2_draw_l90_r2</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[21]">u8g2_draw_l90_r3</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[23]">u8g2_draw_mirror_vertical_r0</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[13e]">u8g2_font_calc_vref_bottom</a> from u8g2_font.o(.text) referenced from u8g2_font.o(.text)
 <LI><a href="#[140]">u8g2_font_calc_vref_center</a> from u8g2_font.o(.text) referenced from u8g2_font.o(.text)
 <LI><a href="#[13d]">u8g2_font_calc_vref_font</a> from u8g2_font.o(.text) referenced from u8g2_font.o(.text)
 <LI><a href="#[13f]">u8g2_font_calc_vref_top</a> from u8g2_font.o(.text) referenced from u8g2_font.o(.text)
 <LI><a href="#[8]">u8g2_gpio_and_delay</a> from u8x8_byte.o(.text) referenced 2 times from main.o(.text)
 <LI><a href="#[f]">u8g2_ll_hvline_vertical_top_lsb</a> from u8g2_ll_hvline.o(.text) referenced 2 times from u8g2_d_setup.o(.text)
 <LI><a href="#[16]">u8g2_update_dimension_r0</a> from u8g2_setup.o(.text) referenced 3 times from u8g2_setup.o(.constdata)
 <LI><a href="#[19]">u8g2_update_dimension_r1</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[1c]">u8g2_update_dimension_r2</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[1f]">u8g2_update_dimension_r3</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[17]">u8g2_update_page_win_r0</a> from u8g2_setup.o(.text) referenced 3 times from u8g2_setup.o(.constdata)
 <LI><a href="#[1a]">u8g2_update_page_win_r1</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[1d]">u8g2_update_page_win_r2</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[20]">u8g2_update_page_win_r3</a> from u8g2_setup.o(.text) referenced from u8g2_setup.o(.constdata)
 <LI><a href="#[10]">u8x8_ascii_next</a> from u8x8_8x8.o(.text) referenced 5 times from u8g2_font.o(.text)
 <LI><a href="#[9]">u8x8_byte_4wire_sw_spi</a> from u8x8_byte.o(.text) referenced 2 times from main.o(.text)
 <LI><a href="#[d]">u8x8_cad_001</a> from u8x8_cad.o(.text) referenced from u8g2_d_setup.o(.text)
 <LI><a href="#[12]">u8x8_cad_empty</a> from u8x8_cad.o(.text) referenced from u8g2_setup.o(.text)
 <LI><a href="#[b]">u8x8_capture_get_pixel_1</a> from u8x8_capture.o(.text) referenced from u8g2_buffer.o(.text)
 <LI><a href="#[c]">u8x8_capture_get_pixel_2</a> from u8x8_capture.o(.text) referenced from u8g2_buffer.o(.text)
 <LI><a href="#[13]">u8x8_d_null_cb</a> from u8x8_setup.o(.text) referenced from u8g2_setup.o(.text)
 <LI><a href="#[e]">u8x8_d_ssd1306_128x64_noname</a> from u8x8_d_ssd1306_128x64_noname.o(.text) referenced from u8g2_d_setup.o(.text)
 <LI><a href="#[19c]">u8x8_dummy_cb</a> from u8x8_setup.o(.text) referenced from u8x8_setup.o(.text)
 <LI><a href="#[11]">u8x8_utf8_next</a> from u8x8_8x8.o(.text) referenced 5 times from u8g2_font.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_Handler
</UL>

<P><STRONG><a name="[1c0]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[25]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1ae]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1c1]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1c2]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1c3]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1c4]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[1c5]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1c6]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[5b]"></a>CPUcpsid</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterDisable
</UL>

<P><STRONG><a name="[1c7]"></a>CPUprimask</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)

<P><STRONG><a name="[59]"></a>CPUcpsie</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cpu.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterEnable
</UL>

<P><STRONG><a name="[63]"></a>CPUwfi</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDeepSleep
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlSleep
</UL>

<P><STRONG><a name="[5d]"></a>CPUbasepriSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPriorityMaskSet
</UL>

<P><STRONG><a name="[5f]"></a>CPUbasepriGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPriorityMaskGet
</UL>

<P><STRONG><a name="[27]"></a>SysCtlDelay</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockSet
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[28]"></a>ADCIntRegister</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ADCIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ADCIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[2c]"></a>ADCIntUnregister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ADCIntNumberGet
</UL>

<P><STRONG><a name="[1c8]"></a>ADCIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[c7]"></a>ADCIntEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1c9]"></a>ADCIntStatus</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>ADCIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[c6]"></a>ADCSequenceEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1ca]"></a>ADCSequenceDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[c2]"></a>ADCSequenceConfigure</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADCSequenceConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[c4]"></a>ADCSequenceStepConfigure</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADCSequenceStepConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1cb]"></a>ADCSequenceOverflow</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1cc]"></a>ADCSequenceOverflowClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1cd]"></a>ADCSequenceUnderflow</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1ce]"></a>ADCSequenceUnderflowClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1cf]"></a>ADCSequenceDataGet</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d0]"></a>ADCProcessorTrigger</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d1]"></a>ADCSoftwareOversampleConfigure</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d2]"></a>ADCSoftwareOversampleStepConfigure</STRONG> (Thumb, 110 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d3]"></a>ADCSoftwareOversampleDataGet</STRONG> (Thumb, 54 bytes, Stack size 20 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[c3]"></a>ADCHardwareOversampleConfigure</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1d4]"></a>ADCComparatorConfigure</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d5]"></a>ADCComparatorRegionSet</STRONG> (Thumb, 16 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d6]"></a>ADCComparatorReset</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d7]"></a>ADCComparatorIntDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d8]"></a>ADCComparatorIntEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d9]"></a>ADCComparatorIntStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1da]"></a>ADCComparatorIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1db]"></a>ADCIntDisableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1dc]"></a>ADCIntEnableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1dd]"></a>ADCIntStatusEx</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1de]"></a>ADCIntClearEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1df]"></a>ADCReferenceSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1e0]"></a>ADCReferenceGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1e1]"></a>ADCPhaseDelaySet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>ADCPhaseDelayGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[c5]"></a>ADCSequenceDMAEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1e3]"></a>ADCSequenceDMADisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>ADCBusy</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[33]"></a>GPIODirModeSet</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCIR
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLEDSeq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBColumn
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBRow
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeLow
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeHigh
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBDigital
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBAnalog
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUART
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeTimer
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeSSI
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeQEI
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePWM
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECITx
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECIRx
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLPC
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLCD
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutputOD
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOInput
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeFan
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetMII
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetLED
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEPI
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeComparator
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCAN
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[1e5]"></a>GPIODirModeGet</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1e6]"></a>GPIOIntTypeSet</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>GPIOIntTypeGet</STRONG> (Thumb, 84 bytes, Stack size 20 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[34]"></a>GPIOPadConfigSet</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIOPadConfigSet
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCIR
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLEDSeq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBColumn
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBRow
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeLow
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeHigh
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBDigital
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBAnalog
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUART
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeTimer
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeSSI
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeQEI
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePWM
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECITx
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECIRx
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLPC
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLCD
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutputOD
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOInput
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeFan
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetMII
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetLED
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEPI
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeComparator
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCAN
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[1e8]"></a>GPIOPadConfigGet</STRONG> (Thumb, 206 bytes, Stack size 20 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1e9]"></a>GPIOIntEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1ea]"></a>GPIOIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1eb]"></a>GPIOIntStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1ec]"></a>GPIOIntClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>GPIOIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GPIOIntNumberGet
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[31]"></a>GPIOIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GPIOIntNumberGet
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[b6]"></a>GPIOPinRead</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
</UL>

<P><STRONG><a name="[b8]"></a>GPIOPinWrite</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_send_byte
</UL>

<P><STRONG><a name="[32]"></a>GPIOPinTypeADC</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeADC &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[35]"></a>GPIOPinTypeCAN</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[36]"></a>GPIOPinTypeComparator</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[37]"></a>GPIOPinTypeEPI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[38]"></a>GPIOPinTypeEthernetLED</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[39]"></a>GPIOPinTypeEthernetMII</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3a]"></a>GPIOPinTypeFan</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3b]"></a>GPIOPinTypeGPIOInput</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3c]"></a>GPIOPinTypeGPIOOutput</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3d]"></a>GPIOPinTypeGPIOOutputOD</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3e]"></a>GPIOPinTypeI2C</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeI2C &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[3f]"></a>GPIOPinTypeI2CSCL</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeI2CSCL &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[40]"></a>GPIOPinTypeLCD</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[41]"></a>GPIOPinTypeLPC</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[42]"></a>GPIOPinTypePECIRx</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[43]"></a>GPIOPinTypePECITx</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[44]"></a>GPIOPinTypePWM</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[45]"></a>GPIOPinTypeQEI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[46]"></a>GPIOPinTypeSSI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[47]"></a>GPIOPinTypeTimer</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[48]"></a>GPIOPinTypeUART</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[49]"></a>GPIOPinTypeUSBAnalog</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4a]"></a>GPIOPinTypeUSBDigital</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4b]"></a>GPIOPinTypeWakeHigh</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4c]"></a>GPIOPinTypeWakeLow</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4d]"></a>GPIOPinTypeKBRow</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4e]"></a>GPIOPinTypeKBColumn</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[4f]"></a>GPIOPinTypeLEDSeq</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[50]"></a>GPIOPinTypeCIR</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[1ed]"></a>GPIOPinWakeStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[c8]"></a>GPIOPinConfigure</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIOPinConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[1ee]"></a>GPIODMATriggerEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1ef]"></a>GPIODMATriggerDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1f0]"></a>GPIOADCTriggerEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1f1]"></a>GPIOADCTriggerDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[52]"></a>I2CMasterEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
</UL>

<P><STRONG><a name="[51]"></a>I2CMasterInitExpClk</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, i2c.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2CMasterInitExpClk
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[54]"></a>I2CSlaveEnable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CSlaveInit
</UL>

<P><STRONG><a name="[53]"></a>I2CSlaveInit</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CSlaveEnable
</UL>

<P><STRONG><a name="[1f2]"></a>I2CSlaveAddressSet</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f3]"></a>I2CMasterDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f4]"></a>I2CSlaveDisable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[55]"></a>I2CIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_I2CIntNumberGet
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[57]"></a>I2CIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_I2CIntNumberGet
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[1f5]"></a>I2CMasterIntEnable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f6]"></a>I2CMasterIntEnableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f7]"></a>I2CSlaveIntEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f8]"></a>I2CSlaveIntEnableEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1f9]"></a>I2CMasterIntDisable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1fa]"></a>I2CMasterIntDisableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1fb]"></a>I2CSlaveIntDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1fc]"></a>I2CSlaveIntDisableEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1fd]"></a>I2CMasterIntStatus</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1fe]"></a>I2CMasterIntStatusEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1ff]"></a>I2CSlaveIntStatus</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[200]"></a>I2CSlaveIntStatusEx</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[201]"></a>I2CMasterIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[202]"></a>I2CMasterIntClearEx</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[203]"></a>I2CSlaveIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[204]"></a>I2CSlaveIntClearEx</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[ca]"></a>I2CMasterSlaveAddrSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[205]"></a>I2CMasterLineStateGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[cd]"></a>I2CMasterBusy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[206]"></a>I2CMasterBusBusy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>I2CMasterControl</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[207]"></a>I2CMasterErr</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[cb]"></a>I2CMasterDataPut</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[cf]"></a>I2CMasterDataGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>

<P><STRONG><a name="[208]"></a>I2CMasterTimeoutSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[209]"></a>I2CSlaveACKOverride</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20a]"></a>I2CSlaveACKValueSet</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20b]"></a>I2CSlaveStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20c]"></a>I2CSlaveDataPut</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20d]"></a>I2CSlaveDataGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20e]"></a>I2CTxFIFOConfigSet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[20f]"></a>I2CTxFIFOFlush</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[210]"></a>I2CRxFIFOConfigSet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[211]"></a>I2CRxFIFOFlush</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[212]"></a>I2CFIFOStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[213]"></a>I2CFIFODataPut</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[214]"></a>I2CFIFODataPutNonBlocking</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[215]"></a>I2CFIFODataGet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[216]"></a>I2CFIFODataGetNonBlocking</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[217]"></a>I2CMasterBurstLengthSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[218]"></a>I2CMasterBurstCountGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[219]"></a>I2CMasterGlitchFilterConfigSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[21a]"></a>I2CSlaveFIFOEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[21b]"></a>I2CSlaveFIFODisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[58]"></a>IntMasterEnable</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IntMasterEnable
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUcpsie
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[5a]"></a>IntMasterDisable</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUcpsid
</UL>

<P><STRONG><a name="[2a]"></a>IntRegister</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = IntRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntRegister
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntRegister
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntRegister
</UL>

<P><STRONG><a name="[2e]"></a>IntUnregister</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntUnregister
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntUnregister
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntUnregister
</UL>

<P><STRONG><a name="[21c]"></a>IntPriorityGroupingSet</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[21d]"></a>IntPriorityGroupingGet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[a9]"></a>IntPrioritySet</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IntPrioritySet
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[21e]"></a>IntPriorityGet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[2b]"></a>IntEnable</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, interrupt.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntRegister
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntRegister
</UL>

<P><STRONG><a name="[2d]"></a>IntDisable</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntUnregister
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntUnregister
</UL>

<P><STRONG><a name="[21f]"></a>IntIsEnabled</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[220]"></a>IntPendSet</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[221]"></a>IntPendClear</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[5c]"></a>IntPriorityMaskSet</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUbasepriSet
</UL>

<P><STRONG><a name="[5e]"></a>IntPriorityMaskGet</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUbasepriGet
</UL>

<P><STRONG><a name="[222]"></a>IntTrigger</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[223]"></a>SysCtlSRAMSizeGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[224]"></a>SysCtlFlashSizeGet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[225]"></a>SysCtlFlashSectorSizeGet</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[226]"></a>SysCtlPeripheralPresent</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[9f]"></a>SysCtlPeripheralReady</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, sysctl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[227]"></a>SysCtlPeripheralPowerOn</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[228]"></a>SysCtlPeripheralPowerOff</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[229]"></a>SysCtlPeripheralReset</STRONG> (Thumb, 106 bytes, Stack size 12 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>SysCtlPeripheralEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[22a]"></a>SysCtlPeripheralDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22b]"></a>SysCtlPeripheralSleepEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22c]"></a>SysCtlPeripheralSleepDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22d]"></a>SysCtlPeripheralDeepSleepEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22e]"></a>SysCtlPeripheralDeepSleepDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22f]"></a>SysCtlPeripheralClockGating</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[60]"></a>SysCtlIntRegister</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[61]"></a>SysCtlIntUnregister</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[230]"></a>SysCtlIntEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[231]"></a>SysCtlIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>SysCtlIntClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[233]"></a>SysCtlIntStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>SysCtlLDOSleepSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>SysCtlLDOSleepGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>SysCtlLDODeepSleepSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[237]"></a>SysCtlLDODeepSleepGet</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[238]"></a>SysCtlSleepPowerSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[239]"></a>SysCtlDeepSleepPowerSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[23a]"></a>SysCtlReset</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[62]"></a>SysCtlSleep</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUwfi
</UL>

<P><STRONG><a name="[64]"></a>SysCtlDeepSleep</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUwfi
</UL>

<P><STRONG><a name="[23b]"></a>SysCtlResetCauseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[23c]"></a>SysCtlResetCauseClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[23d]"></a>SysCtlMOSCConfigSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[23e]"></a>SysCtlPIOSCCalibrate</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>SysCtlResetBehaviorSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>SysCtlResetBehaviorGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[65]"></a>SysCtlClockFreqSet</STRONG> (Thumb, 646 bytes, Stack size 48 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlFrequencyGet
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlMemTimingGet
</UL>

<P><STRONG><a name="[68]"></a>SysCtlClockSet</STRONG> (Thumb, 362 bytes, Stack size 24 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
</UL>

<P><STRONG><a name="[b1]"></a>SysCtlClockGet</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, sysctl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysCtlClockGet
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[241]"></a>SysCtlDeepSleepClockSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>SysCtlDeepSleepClockConfigSet</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlMemTimingGet
</UL>

<P><STRONG><a name="[242]"></a>SysCtlPWMClockSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[243]"></a>SysCtlPWMClockGet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[244]"></a>SysCtlADCSpeedSet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[245]"></a>SysCtlADCSpeedGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[246]"></a>SysCtlGPIOAHBEnable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[247]"></a>SysCtlGPIOAHBDisable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[248]"></a>SysCtlUSBPLLEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[249]"></a>SysCtlUSBPLLDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24a]"></a>SysCtlVoltageEventConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24b]"></a>SysCtlVoltageEventStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24c]"></a>SysCtlVoltageEventClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24d]"></a>SysCtlNMIStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24e]"></a>SysCtlNMIClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[24f]"></a>SysCtlClockOutConfig</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[250]"></a>SysCtlAltClkConfig</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[dd]"></a>SysTickEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[251]"></a>SysTickDisable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[6a]"></a>SysTickIntRegister</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, systick.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SysTickIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[6b]"></a>SysTickIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, systick.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
</UL>

<P><STRONG><a name="[dc]"></a>SysTickIntEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[252]"></a>SysTickIntDisable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[db]"></a>SysTickPeriodSet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[253]"></a>SysTickPeriodGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[254]"></a>SysTickValueGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>TimerEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[255]"></a>TimerDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[b0]"></a>TimerConfigure</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[256]"></a>TimerControlLevel</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>TimerControlTrigger</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TimerControlTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[257]"></a>TimerControlEvent</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[258]"></a>TimerControlStall</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[259]"></a>TimerControlWaitOnTrigger</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25a]"></a>TimerRTCEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25b]"></a>TimerRTCDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25c]"></a>TimerClockSourceSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25d]"></a>TimerClockSourceGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25e]"></a>TimerPrescaleSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[25f]"></a>TimerPrescaleGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[260]"></a>TimerPrescaleMatchSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[261]"></a>TimerPrescaleMatchGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[b2]"></a>TimerLoadSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[262]"></a>TimerLoadGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[263]"></a>TimerLoadSet64</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[264]"></a>TimerLoadGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[265]"></a>TimerValueGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[266]"></a>TimerValueGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[267]"></a>TimerMatchSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[268]"></a>TimerMatchGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[269]"></a>TimerMatchSet64</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[26a]"></a>TimerMatchGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[6c]"></a>TimerIntRegister</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TimerIntRegister &rArr; _TimerIntNumberGet
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_TimerIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[6e]"></a>TimerIntUnregister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_TimerIntNumberGet
</UL>

<P><STRONG><a name="[b3]"></a>TimerIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[26b]"></a>TimerIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[26c]"></a>TimerIntStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>TimerIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[26d]"></a>TimerSynchronize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[26e]"></a>TimerADCEventSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[26f]"></a>TimerADCEventGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[270]"></a>TimerDMAEventSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[271]"></a>TimerDMAEventGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[272]"></a>UARTParityModeSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[273]"></a>UARTParityModeGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[274]"></a>UARTFIFOLevelSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[275]"></a>UARTFIFOLevelGet</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>UARTEnable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTConfigSetExpClk
</UL>

<P><STRONG><a name="[70]"></a>UARTDisable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTConfigSetExpClk
</UL>

<P><STRONG><a name="[6f]"></a>UARTConfigSetExpClk</STRONG> (Thumb, 76 bytes, Stack size 20 bytes, uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTDisable
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEnable
</UL>

<P><STRONG><a name="[276]"></a>UARTConfigGetExpClk</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>UARTFIFOEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[277]"></a>UARTFIFODisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[278]"></a>UARTEnableSIR</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[279]"></a>UARTDisableSIR</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27a]"></a>UARTSmartCardEnable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27b]"></a>UARTSmartCardDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27c]"></a>UARTModemControlSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27d]"></a>UARTModemControlClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27e]"></a>UARTModemControlGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[27f]"></a>UARTModemStatusGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[280]"></a>UARTFlowControlSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[281]"></a>UARTFlowControlGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[282]"></a>UARTTxIntModeSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[283]"></a>UARTTxIntModeGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[284]"></a>UARTCharsAvail</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[285]"></a>UARTSpaceAvail</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[286]"></a>UARTCharGetNonBlocking</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>UARTCharGet</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fgetc
</UL>

<P><STRONG><a name="[287]"></a>UARTCharPutNonBlocking</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>UARTCharPut</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[288]"></a>UARTBreakCtl</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[289]"></a>UARTBusy</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>UARTIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UARTIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_UARTIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[74]"></a>UARTIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_UARTIntNumberGet
</UL>

<P><STRONG><a name="[aa]"></a>UARTIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[28a]"></a>UARTIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>UARTIntStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[9a]"></a>UARTIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[28b]"></a>UARTDMAEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[28c]"></a>UARTDMADisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[28d]"></a>UARTRxErrorGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[28e]"></a>UARTRxErrorClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[a7]"></a>UARTClockSourceSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[28f]"></a>UARTClockSourceGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[290]"></a>UART9BitEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[291]"></a>UART9BitDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[292]"></a>UART9BitAddrSet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[293]"></a>UART9BitAddrSend</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[a0]"></a>uDMAEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[294]"></a>uDMADisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[295]"></a>uDMAErrorStatusGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[296]"></a>uDMAErrorStatusClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[a5]"></a>uDMAChannelEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[297]"></a>uDMAChannelDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[298]"></a>uDMAChannelIsEnabled</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>uDMAControlBaseSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[299]"></a>uDMAControlBaseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[29a]"></a>uDMAControlAlternateBaseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[29b]"></a>uDMAChannelRequest</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[a3]"></a>uDMAChannelAttributeEnable</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
</UL>

<P><STRONG><a name="[a2]"></a>uDMAChannelAttributeDisable</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[29c]"></a>uDMAChannelAttributeGet</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>uDMAChannelControlSet</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, udma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = uDMAChannelControlSet
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[9c]"></a>uDMAChannelTransferSet</STRONG> (Thumb, 170 bytes, Stack size 28 bytes, udma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = uDMAChannelTransferSet
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[29d]"></a>uDMAChannelScatterGatherSet</STRONG> (Thumb, 82 bytes, Stack size 20 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[29e]"></a>uDMAChannelSizeGet</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>uDMAChannelModeGet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[29f]"></a>uDMAChannelSelectSecondary</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[2a0]"></a>uDMAChannelSelectDefault</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[75]"></a>uDMAIntRegister</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, udma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[76]"></a>uDMAIntUnregister</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, udma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[2a1]"></a>uDMAIntStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[2a2]"></a>uDMAIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[2a3]"></a>uDMAChannelAssign</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[77]"></a>usart_send</STRONG> (Thumb, 302 bytes, Stack size 72 bytes, main.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[79]"></a>draw_simple_ui</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, main.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFont
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawStr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHLine
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFrame
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawBox
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[82]"></a>draw_geometry_demo</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = draw_geometry_demo &rArr; u8g2_DrawStr &rArr; u8g2_draw_string &rArr; u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFont
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawStr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHLine
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFrame
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6]"></a>main</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = main &rArr; draw_geometry_demo &rArr; u8g2_DrawStr &rArr; u8g2_draw_string &rArr; u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetPowerSave
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_InitDisplay
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_128x64_noname_f
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearDisplay
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[a6]"></a>UARTStdioConfig</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, uartstdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UARTStdioConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[92]"></a>UARTwrite</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTvprintf
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTgets
</UL>

<P><STRONG><a name="[91]"></a>UARTgets</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTwrite
</UL>

<P><STRONG><a name="[2a4]"></a>UARTgetc</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, uartstdio.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>UARTvprintf</STRONG> (Thumb, 574 bytes, Stack size 64 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTwrite
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTprintf
</UL>

<P><STRONG><a name="[94]"></a>UARTprintf</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTvprintf
</UL>

<P><STRONG><a name="[14]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTCharPut
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[96]"></a>fgetc</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, uart_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTCharGet
</UL>

<P><STRONG><a name="[98]"></a>UART0_IRQHandler</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART0_IRQHandler &rArr; uDMAChannelTransferSet
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelModeGet
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntClear
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_app.o(.text)
</UL>
<P><STRONG><a name="[0]"></a>Uart_Proc</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Uart_Proc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[85]"></a>ConfigureDMA</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ConfigureDMA &rArr; uDMAChannelTransferSet
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelControlSet
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeDisable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeEnable
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAControlBaseSet
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelEnable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAEnable
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>ConfigureUART</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ConfigureUART &rArr; UARTIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPrioritySet
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTStdioConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTClockSourceSet
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntEnable
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTFIFOEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>TIMER0A_Handler</STRONG> (Thumb, 318 bytes, Stack size 40 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = TIMER0A_Handler &rArr; MPU6050_Read_Data &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntClear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_app.o(.text)
</UL>
<P><STRONG><a name="[8a]"></a>Time_init</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Time_init &rArr; TimerIntRegister &rArr; _TimerIntNumberGet
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPrioritySet
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterEnable
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntEnable
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerLoadSet
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerConfigure
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEnable
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8f]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2a5]"></a>scheduler_run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, scheduler.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>Key_Init</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, key_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Key_Init &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b5]"></a>Key_Read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, key_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinRead
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[b7]"></a>Key_Proc</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, key_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[b9]"></a>Adc_Proc</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, adc_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[bb]"></a>ADC_Timer_Init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Timer_Init &rArr; SysCtlClockGet
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerLoadSet
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerControlTrigger
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerConfigure
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEnable
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[bd]"></a>ADC0_Handler</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ADC0_Handler &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntClear
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelModeGet
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> adc_app.o(.text)
</UL>
<P><STRONG><a name="[86]"></a>ADC_DMA_Init</STRONG> (Thumb, 300 bytes, Stack size 8 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ADC_DMA_Init &rArr; ADCIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceDMAEnable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCHardwareOversampleConfigure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceStepConfigure
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceConfigure
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceEnable
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntEnable
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelControlSet
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeDisable
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAControlBaseSet
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelEnable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAEnable
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>Init_I2C</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Init_I2C &rArr; GPIOPinTypeI2CSCL &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c9]"></a>i2cWriteData</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2cWriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>

<P><STRONG><a name="[ce]"></a>i2cRead</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Double_ReadI2C
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_ReadI2C
</UL>

<P><STRONG><a name="[d0]"></a>i2cWrite</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2cWrite &rArr; i2cWriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_WriteI2C
</UL>

<P><STRONG><a name="[d1]"></a>i2cReadData</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2cReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
</UL>

<P><STRONG><a name="[d2]"></a>Single_WriteI2C</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>

<P><STRONG><a name="[d3]"></a>Single_ReadI2C</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Single_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
</UL>

<P><STRONG><a name="[d4]"></a>Double_ReadI2C</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
</UL>

<P><STRONG><a name="[d5]"></a>Init_I2C0</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>

<P><STRONG><a name="[d6]"></a>i2c0WriteData</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Write
</UL>

<P><STRONG><a name="[d7]"></a>i2c0Read</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>

<P><STRONG><a name="[d8]"></a>i2c0Write</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
</UL>

<P><STRONG><a name="[d9]"></a>i2c0ReadData</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>

<P><STRONG><a name="[da]"></a>IMU_Calibration</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IMU_Calibration &rArr; Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Double_ReadI2C
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
</UL>

<P><STRONG><a name="[89]"></a>ICM20608_Init</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ICM20608_Init &rArr; IMU_Calibration &rArr; Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_ReadI2C
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ac]"></a>MPU6050_Read_Data</STRONG> (Thumb, 284 bytes, Stack size 64 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MPU6050_Read_Data &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[83]"></a>initTime</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = initTime &rArr; SysTickIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickPeriodSet
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntEnable
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntRegister
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickEnable
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[df]"></a>micros</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, systicktime.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Period
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[de]"></a>delayMicroseconds</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>

<P><STRONG><a name="[e0]"></a>delay</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[2a6]"></a>millis</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, systicktime.o(.text), UNUSED)

<P><STRONG><a name="[e1]"></a>Delay_Ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Delay_Ms &rArr; delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[90]"></a>delay_ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = delay_ms &rArr; Delay_Ms &rArr; delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
</UL>

<P><STRONG><a name="[e2]"></a>delay_us</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_us &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_send_byte
</UL>

<P><STRONG><a name="[e3]"></a>Delay_Us</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, systicktime.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[e4]"></a>Test_Period</STRONG> (Thumb, 68 bytes, Stack size 4 bytes, systicktime.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
</UL>

<P><STRONG><a name="[ec]"></a>calculate_adaptive_alpha</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[e6]"></a>invSqrt</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[2a7]"></a>kalman_filter</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[2a8]"></a>imu_init</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[ad]"></a>imu_update</STRONG> (Thumb, 1414 bytes, Stack size 112 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = imu_update &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invSqrt
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;compute_rotation_matrix
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[ed]"></a>ekf_update</STRONG> (Thumb, 364 bytes, Stack size 48 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ekf_update
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[2a9]"></a>is_yaw_outlier</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[ae]"></a>imu_get_euler_angles</STRONG> (Thumb, 468 bytes, Stack size 80 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = imu_get_euler_angles &rArr; ekf_update
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ekf_update
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_adaptive_alpha
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[2aa]"></a>Init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, oled_app.o(.text), UNUSED)

<P><STRONG><a name="[f0]"></a>LCD_DisplayStringLine</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, oled_app.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[ee]"></a>LcdSprintf</STRONG> (Thumb, 42 bytes, Stack size 56 bytes, oled_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = LcdSprintf &rArr; vsprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[f1]"></a>spi_send_byte</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, oled_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[1]"></a>Oled_Proc</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, oled_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Oled_Proc &rArr; LcdSprintf &rArr; vsprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[80]"></a>u8g2_DrawBox</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, u8g2_box.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRBox
</UL>

<P><STRONG><a name="[7f]"></a>u8g2_DrawFrame</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, u8g2_box.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = u8g2_DrawFrame &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
</UL>

<P><STRONG><a name="[f4]"></a>u8g2_DrawRBox</STRONG> (Thumb, 318 bytes, Stack size 72 bytes, u8g2_box.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawBox
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawDisc
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[f6]"></a>u8g2_DrawRFrame</STRONG> (Thumb, 332 bytes, Stack size 64 bytes, u8g2_box.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHLine
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawVLine
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[7a]"></a>u8g2_ClearBuffer</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_ClearBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_NextPage
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_FirstPage
</UL>

<P><STRONG><a name="[81]"></a>u8g2_SendBuffer</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = u8g2_SendBuffer &rArr; u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_RefreshDisplay
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
</UL>

<P><STRONG><a name="[ff]"></a>u8g2_SetBufferCurrTileRow</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_SetBufferCurrTileRow
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearDisplay
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_NextPage
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_FirstPage
</UL>

<P><STRONG><a name="[fe]"></a>u8g2_FirstPage</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8g2_FirstPage &rArr; u8g2_ClearBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetBufferCurrTileRow
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearDisplay
</UL>

<P><STRONG><a name="[100]"></a>u8g2_NextPage</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = u8g2_NextPage &rArr; u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_RefreshDisplay
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetBufferCurrTileRow
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearDisplay
</UL>

<P><STRONG><a name="[101]"></a>u8g2_UpdateDisplayArea</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
</UL>

<P><STRONG><a name="[102]"></a>u8g2_UpdateDisplay</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>

<P><STRONG><a name="[103]"></a>u8g2_WriteBufferPBM</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_pbm_pre
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_pbm_buffer
</UL>

<P><STRONG><a name="[106]"></a>u8g2_WriteBufferXBM</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_xbm_pre
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_xbm_buffer
</UL>

<P><STRONG><a name="[109]"></a>u8g2_WriteBufferPBM2</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_pbm_pre
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_pbm_buffer
</UL>

<P><STRONG><a name="[10a]"></a>u8g2_WriteBufferXBM2</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, u8g2_buffer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_xbm_pre
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_xbm_buffer
</UL>

<P><STRONG><a name="[f7]"></a>u8g2_DrawCircle</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRFrame
</UL>

<P><STRONG><a name="[f5]"></a>u8g2_DrawDisc</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_disc
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRBox
</UL>

<P><STRONG><a name="[112]"></a>u8g2_DrawEllipse</STRONG> (Thumb, 70 bytes, Stack size 40 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_ellipse
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[115]"></a>u8g2_DrawFilledEllipse</STRONG> (Thumb, 70 bytes, Stack size 40 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_filled_ellipse
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[8e]"></a>u8g2_ClearDisplay</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_cleardisplay.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = u8g2_ClearDisplay &rArr; u8g2_NextPage &rArr; u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_NextPage
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_FirstPage
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetBufferCurrTileRow
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[117]"></a>u8g2_m_16_8_f</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, u8g2_d_memory.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_128x64_noname_f
</UL>

<P><STRONG><a name="[8b]"></a>u8g2_Setup_ssd1306_128x64_noname_f</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, u8g2_d_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_Setup_ssd1306_128x64_noname_f &rArr; u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_m_16_8_f
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_init
</UL>

<P><STRONG><a name="[119]"></a>u8g2_init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, u8g2_d_setup.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetPowerSave
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_InitDisplay
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_128x64_noname_f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetContrast
</UL>

<P><STRONG><a name="[11b]"></a>u8g2_read_font_info</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_read_font_info
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_word
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFont
</UL>

<P><STRONG><a name="[11e]"></a>u8g2_GetFontSize</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_word
</UL>

<P><STRONG><a name="[2ab]"></a>u8g2_GetFontBBXWidth</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[2ac]"></a>u8g2_GetFontBBXHeight</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[2ad]"></a>u8g2_GetFontBBXOffX</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[2ae]"></a>u8g2_GetFontBBXOffY</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[2af]"></a>u8g2_GetFontCapitalAHeight</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[120]"></a>u8g2_font_decode_get_unsigned_bits</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_font_decode_get_unsigned_bits
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_signed_bits
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_setup_decode
</UL>

<P><STRONG><a name="[11f]"></a>u8g2_font_decode_get_signed_bits</STRONG> (Thumb, 36 bytes, Stack size 20 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = u8g2_font_decode_get_signed_bits &rArr; u8g2_font_decode_get_unsigned_bits
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_unsigned_bits
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphHorizontalProperties
</UL>

<P><STRONG><a name="[123]"></a>u8g2_add_vector_y</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_add_vector_y
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_len
</UL>

<P><STRONG><a name="[122]"></a>u8g2_add_vector_x</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_add_vector_x
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_len
</UL>

<P><STRONG><a name="[121]"></a>u8g2_font_decode_len</STRONG> (Thumb, 176 bytes, Stack size 56 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_add_vector_x
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_add_vector_y
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
</UL>

<P><STRONG><a name="[124]"></a>u8g2_font_2x_decode_len</STRONG> (Thumb, 212 bytes, Stack size 56 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
</UL>

<P><STRONG><a name="[126]"></a>u8g2_font_decode_glyph</STRONG> (Thumb, 386 bytes, Stack size 64 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_len
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_add_vector_x
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_add_vector_y
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_signed_bits
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_unsigned_bits
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_setup_decode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_draw_glyph
</UL>

<P><STRONG><a name="[127]"></a>u8g2_font_2x_decode_glyph</STRONG> (Thumb, 242 bytes, Stack size 64 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_len
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_signed_bits
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_unsigned_bits
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_setup_decode
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_draw_glyph
</UL>

<P><STRONG><a name="[128]"></a>u8g2_font_get_glyph_data</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8g2_font_get_glyph_data
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_word
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsGlyph
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphHorizontalProperties
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_all_valid
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_draw_glyph
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_draw_glyph
</UL>

<P><STRONG><a name="[12b]"></a>u8g2_IsGlyph</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
</UL>

<P><STRONG><a name="[12c]"></a>u8g2_GetGlyphWidth</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_signed_bits
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_setup_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtUTF8
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtendedUTF8
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_string_width
</UL>

<P><STRONG><a name="[2b0]"></a>u8g2_SetFontMode</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[12d]"></a>u8g2_DrawGlyph</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_draw_glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtUTF8
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtendedUTF8
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string
</UL>

<P><STRONG><a name="[12e]"></a>u8g2_DrawGlyphX2</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_draw_glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string_2x
</UL>

<P><STRONG><a name="[7c]"></a>u8g2_DrawStr</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = u8g2_DrawStr &rArr; u8g2_draw_string &rArr; u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
</UL>

<P><STRONG><a name="[132]"></a>u8g2_DrawStrX2</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string_2x
</UL>

<P><STRONG><a name="[133]"></a>u8g2_DrawUTF8</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string
</UL>

<P><STRONG><a name="[134]"></a>u8g2_DrawUTF8X2</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string_2x
</UL>

<P><STRONG><a name="[135]"></a>u8g2_DrawExtendedUTF8</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetKerning
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyph
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
</UL>

<P><STRONG><a name="[137]"></a>u8g2_DrawExtUTF8</STRONG> (Thumb, 176 bytes, Stack size 56 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetKerningByTable
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyph
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
</UL>

<P><STRONG><a name="[13a]"></a>u8g2_UpdateRefHeight</STRONG> (Thumb, 136 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFont
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontRefHeightAll
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontRefHeightExtendedText
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontRefHeightText
</UL>

<P><STRONG><a name="[139]"></a>u8g2_SetFontRefHeightText</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateRefHeight
</UL>

<P><STRONG><a name="[13b]"></a>u8g2_SetFontRefHeightExtendedText</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateRefHeight
</UL>

<P><STRONG><a name="[13c]"></a>u8g2_SetFontRefHeightAll</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateRefHeight
</UL>

<P><STRONG><a name="[13d]"></a>u8g2_font_calc_vref_font</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(.text)
</UL>
<P><STRONG><a name="[14b]"></a>u8g2_SetFontPosBaseline</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[13e]"></a>u8g2_font_calc_vref_bottom</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(.text)
</UL>
<P><STRONG><a name="[2b1]"></a>u8g2_SetFontPosBottom</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[13f]"></a>u8g2_font_calc_vref_top</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(.text)
</UL>
<P><STRONG><a name="[2b2]"></a>u8g2_SetFontPosTop</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[140]"></a>u8g2_font_calc_vref_center</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(.text)
</UL>
<P><STRONG><a name="[2b3]"></a>u8g2_SetFontPosCenter</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>u8g2_SetFont</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_SetFont &rArr; u8g2_read_font_info
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateRefHeight
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_read_font_info
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
</UL>

<P><STRONG><a name="[142]"></a>u8g2_IsAllValidUTF8</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_all_valid
</UL>

<P><STRONG><a name="[145]"></a>u8g2_GetStrX</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphHorizontalProperties
</UL>

<P><STRONG><a name="[146]"></a>u8g2_GetStrWidth</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_string_width
</UL>

<P><STRONG><a name="[147]"></a>u8g2_GetUTF8Width</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_string_width
</UL>

<P><STRONG><a name="[2b4]"></a>u8g2_SetFontDirection</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, u8g2_font.o(.text), UNUSED)

<P><STRONG><a name="[150]"></a>u8g2_draw_hv_line_2dir</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, u8g2_hvline.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_draw_hv_line_2dir
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r3
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r2
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r1
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_mirror_vertical_r0
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_mirrorr_r0
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r0
</UL>

<P><STRONG><a name="[f3]"></a>u8g2_DrawHVLine</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, u8g2_hvline.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_clip_intersection2
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHLine
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFrame
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawBox
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_len
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_len
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawPixel
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawVLine
</UL>

<P><STRONG><a name="[7d]"></a>u8g2_DrawHLine</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, u8g2_hvline.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = u8g2_DrawHLine &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_geometry_demo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRFrame
</UL>

<P><STRONG><a name="[f8]"></a>u8g2_DrawVLine</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, u8g2_hvline.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_filled_ellipse_section
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_disc_section
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRFrame
</UL>

<P><STRONG><a name="[10c]"></a>u8g2_DrawPixel</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, u8g2_hvline.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_ellipse_section
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle_section
</UL>

<P><STRONG><a name="[2b5]"></a>u8g2_SetDrawColor</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, u8g2_hvline.o(.text), UNUSED)

<P><STRONG><a name="[149]"></a>u8g2_is_intersection_decision_tree</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, u8g2_intersection.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_is_intersection_decision_tree
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>

<P><STRONG><a name="[f2]"></a>u8g2_IsIntersection</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, u8g2_intersection.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_intersection_decision_tree
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFrame
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawBox
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFilledEllipse
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawEllipse
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawDisc
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRFrame
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawRBox
</UL>

<P><STRONG><a name="[136]"></a>u8g2_GetKerning</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, u8g2_kerning.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtendedUTF8
</UL>

<P><STRONG><a name="[138]"></a>u8g2_GetKerningByTable</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, u8g2_kerning.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtUTF8
</UL>

<P><STRONG><a name="[f]"></a>u8g2_ll_hvline_vertical_top_lsb</STRONG> (Thumb, 238 bytes, Stack size 32 bytes, u8g2_ll_hvline.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_ll_hvline_vertical_top_lsb
</UL>
<BR>[Address Reference Count : 2]<UL><LI> u8g2_d_setup.o(.text)
<LI> u8g2_setup.o(.text)
</UL>
<P><STRONG><a name="[2b6]"></a>u8g2_ll_hvline_horizontal_right_lsb</STRONG> (Thumb, 190 bytes, Stack size 28 bytes, u8g2_ll_hvline.o(.text), UNUSED)

<P><STRONG><a name="[14a]"></a>u8g2_SetMaxClipWindow</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_SetMaxClipWindow
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[2b7]"></a>u8g2_SetClipWindow</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, u8g2_setup.o(.text), UNUSED)

<P><STRONG><a name="[118]"></a>u8g2_SetupBuffer</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_SetupBuffer &rArr; u8g2_SetMaxClipWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetMaxClipWindow
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontPosBaseline
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_128x64_noname_f
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_null
</UL>

<P><STRONG><a name="[2b8]"></a>u8g2_SetDisplayRotation</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, u8g2_setup.o(.text), UNUSED)

<P><STRONG><a name="[14c]"></a>u8g2_SendF</STRONG> (Thumb, 30 bytes, Stack size 32 bytes, u8g2_setup.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[16]"></a>u8g2_update_dimension_r0</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r0 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[17]"></a>u8g2_update_page_win_r0</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r0 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[19]"></a>u8g2_update_dimension_r1</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r1 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1a]"></a>u8g2_update_page_win_r1</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r1 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1c]"></a>u8g2_update_dimension_r2</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r2 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1d]"></a>u8g2_update_page_win_r2</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r2 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1f]"></a>u8g2_update_dimension_r3</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r3 &rArr; u8g2_update_dimension_common
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_common
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[20]"></a>u8g2_update_page_win_r3</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8g2_update_page_win_r3 &rArr; u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_apply_clip_window
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[18]"></a>u8g2_draw_l90_r0</STRONG> (Thumb, 36 bytes, Stack size 32 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_l90_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[22]"></a>u8g2_draw_l90_mirrorr_r0</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_l90_mirrorr_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[23]"></a>u8g2_draw_mirror_vertical_r0</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_draw_mirror_vertical_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1b]"></a>u8g2_draw_l90_r1</STRONG> (Thumb, 68 bytes, Stack size 40 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r1 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[1e]"></a>u8g2_draw_l90_r2</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r2 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[21]"></a>u8g2_draw_l90_r3</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = u8g2_draw_l90_r3 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.constdata)
</UL>
<P><STRONG><a name="[151]"></a>u8g2_Setup_null</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, u8g2_setup.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[2b9]"></a>u8x8_SetFont</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, u8x8_8x8.o(.text), UNUSED)

<P><STRONG><a name="[152]"></a>u8x8_DrawGlyph</STRONG> (Thumb, 106 bytes, Stack size 48 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_get_glyph_data
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_string
</UL>

<P><STRONG><a name="[155]"></a>u8x8_upscale_byte</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_subglyph
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_subglyph
</UL>

<P><STRONG><a name="[157]"></a>u8x8_Draw2x2Glyph</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_subglyph
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_string
</UL>

<P><STRONG><a name="[159]"></a>u8x8_Draw1x2Glyph</STRONG> (Thumb, 96 bytes, Stack size 40 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_subglyph
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_string
</UL>

<P><STRONG><a name="[130]"></a>u8x8_utf8_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8x8_8x8.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtUTF8
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawExtendedUTF8
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_string_width
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_is_all_valid
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string_2x
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_string
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_GetUTF8Len
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_string
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_string
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_string
</UL>

<P><STRONG><a name="[10]"></a>u8x8_ascii_next</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, u8x8_8x8.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> u8g2_font.o(.text)
<LI> u8x8_8x8.o(.text)
</UL>
<P><STRONG><a name="[11]"></a>u8x8_utf8_next</STRONG> (Thumb, 156 bytes, Stack size 0 bytes, u8x8_8x8.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_GetUTF8Len
</UL>
<BR>[Address Reference Count : 2]<UL><LI> u8g2_font.o(.text)
<LI> u8x8_8x8.o(.text)
</UL>
<P><STRONG><a name="[15b]"></a>u8x8_DrawString</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_string
</UL>

<P><STRONG><a name="[15c]"></a>u8x8_DrawUTF8</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_string
</UL>

<P><STRONG><a name="[15e]"></a>u8x8_Draw2x2String</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_string
</UL>

<P><STRONG><a name="[15f]"></a>u8x8_Draw2x2UTF8</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_string
</UL>

<P><STRONG><a name="[161]"></a>u8x8_Draw1x2String</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_string
</UL>

<P><STRONG><a name="[162]"></a>u8x8_Draw1x2UTF8</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_string
</UL>

<P><STRONG><a name="[163]"></a>u8x8_GetUTF8Len</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_next
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
</UL>

<P><STRONG><a name="[181]"></a>u8x8_byte_SetDC</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_byte.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_byte_SetDC
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_001
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_011
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_100
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gu800_cad_110
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_110
</UL>

<P><STRONG><a name="[165]"></a>u8x8_byte_SendBytes</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_byte.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_byte_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st7920_spi
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[164]"></a>u8x8_byte_SendByte</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, u8x8_byte.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_byte_SendByte &rArr; u8x8_byte_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_001
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc1638_i2c
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc16xx_i2c
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ld7032_i2c
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st75256_i2c
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_i2c
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st7920_spi
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_011
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_100
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gu800_cad_110
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_110
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_empty
</UL>

<P><STRONG><a name="[183]"></a>u8x8_byte_StartTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc1638_i2c
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc16xx_i2c
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ld7032_i2c
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st75256_i2c
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_i2c
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gu800_cad_110
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[184]"></a>u8x8_byte_EndTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc1638_i2c
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_uc16xx_i2c
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ld7032_i2c
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st75256_i2c
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_i2c
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gu800_cad_110
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[2ba]"></a>u8x8_byte_empty</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)

<P><STRONG><a name="[9]"></a>u8x8_byte_4wire_sw_spi</STRONG> (Thumb, 288 bytes, Stack size 40 bytes, u8x8_byte.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_byte_4wire_sw_spi &rArr; u8x8_gpio_call
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Address Reference Count : 2]<UL><LI> main.o(.text)
<LI> u8g2_d_setup.o(.text)
</UL>
<P><STRONG><a name="[167]"></a>u8x8_byte_8bit_6800mode</STRONG> (Thumb, 248 bytes, Stack size 32 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>

<P><STRONG><a name="[168]"></a>u8x8_byte_8bit_8080mode</STRONG> (Thumb, 248 bytes, Stack size 32 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>

<P><STRONG><a name="[169]"></a>u8x8_byte_3wire_sw_spi</STRONG> (Thumb, 300 bytes, Stack size 40 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>

<P><STRONG><a name="[16a]"></a>u8x8_byte_set_ks0108_cs</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_ks0108
</UL>

<P><STRONG><a name="[16b]"></a>u8x8_byte_ks0108</STRONG> (Thumb, 240 bytes, Stack size 32 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_set_ks0108_cs
</UL>

<P><STRONG><a name="[16c]"></a>u8x8_byte_sed1520</STRONG> (Thumb, 234 bytes, Stack size 32 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>

<P><STRONG><a name="[178]"></a>u8x8_byte_sw_i2c</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_byte
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
</UL>

<P><STRONG><a name="[8]"></a>u8g2_gpio_and_delay</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, u8x8_byte.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8g2_gpio_and_delay &rArr; delay_ms &rArr; Delay_Ms &rArr; delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Address Reference Count : 2]<UL><LI> main.o(.text)
<LI> u8g2_d_setup.o(.text)
</UL>
<P><STRONG><a name="[17b]"></a>u8x8_cad_SendCmd</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[17a]"></a>u8x8_cad_SendArg</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_SendArg
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[2bb]"></a>u8x8_cad_SendMultipleArg</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)

<P><STRONG><a name="[17c]"></a>u8x8_cad_SendData</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_cad_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[179]"></a>u8x8_cad_StartTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_StartTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[17d]"></a>u8x8_cad_EndTransfer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_cad_EndTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[14d]"></a>u8x8_cad_vsendf</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_EndTransfer
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_StartTransfer
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendArg
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SendF
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendF
</UL>

<P><STRONG><a name="[17e]"></a>u8x8_SendF</STRONG> (Thumb, 30 bytes, Stack size 32 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_vsendf
</UL>

<P><STRONG><a name="[17f]"></a>u8x8_cad_SendSequence</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_winstar
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_vcomh0
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_noname
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_alt0
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_vcomh0
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1312_128x64_noname
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
</UL>

<P><STRONG><a name="[12]"></a>u8x8_cad_empty</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_cad_empty &rArr; u8x8_byte_SendByte &rArr; u8x8_byte_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.text)
</UL>
<P><STRONG><a name="[180]"></a>u8x8_cad_110</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
</UL>

<P><STRONG><a name="[182]"></a>u8x8_gu800_cad_110</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
</UL>

<P><STRONG><a name="[185]"></a>u8x8_cad_100</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
</UL>

<P><STRONG><a name="[d]"></a>u8x8_cad_001</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, u8x8_cad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_cad_001 &rArr; u8x8_byte_SendByte &rArr; u8x8_byte_SendBytes
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(.text)
</UL>
<P><STRONG><a name="[186]"></a>u8x8_cad_011</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SetDC
</UL>

<P><STRONG><a name="[187]"></a>u8x8_cad_st7920_spi</STRONG> (Thumb, 284 bytes, Stack size 40 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendBytes
</UL>

<P><STRONG><a name="[189]"></a>u8x8_cad_ssd13xx_i2c</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[18a]"></a>u8x8_cad_ssd13xx_fast_i2c</STRONG> (Thumb, 208 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[18b]"></a>u8x8_cad_st75256_i2c</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[18c]"></a>u8x8_cad_ld7032_i2c</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[18d]"></a>u8x8_cad_uc16xx_i2c</STRONG> (Thumb, 322 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[18e]"></a>u8x8_cad_uc1638_i2c</STRONG> (Thumb, 388 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[b]"></a>u8x8_capture_get_pixel_1</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, u8x8_capture.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = u8x8_capture_get_pixel_1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_buffer.o(.text)
</UL>
<P><STRONG><a name="[c]"></a>u8x8_capture_get_pixel_2</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, u8x8_capture.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_capture_get_pixel_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_buffer.o(.text)
</UL>
<P><STRONG><a name="[104]"></a>u8x8_capture_write_pbm_pre</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, u8x8_capture.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utoa
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferPBM2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferPBM
</UL>

<P><STRONG><a name="[105]"></a>u8x8_capture_write_pbm_buffer</STRONG> (Thumb, 88 bytes, Stack size 56 bytes, u8x8_capture.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferPBM2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferPBM
</UL>

<P><STRONG><a name="[107]"></a>u8x8_capture_write_xbm_pre</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, u8x8_capture.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utoa
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferXBM2
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferXBM
</UL>

<P><STRONG><a name="[108]"></a>u8x8_capture_write_xbm_buffer</STRONG> (Thumb, 252 bytes, Stack size 64 bytes, u8x8_capture.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferXBM2
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_WriteBufferXBM
</UL>

<P><STRONG><a name="[e]"></a>u8x8_d_ssd1306_128x64_noname</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = u8x8_d_ssd1306_128x64_noname &rArr; u8x8_d_ssd1306_sh1106_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(.text)
</UL>
<P><STRONG><a name="[193]"></a>u8x8_d_ssd1312_128x64_noname</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[194]"></a>u8x8_d_ssd1306_128x64_vcomh0</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[195]"></a>u8x8_d_ssd1306_128x64_alt0</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[196]"></a>u8x8_d_sh1106_128x64_noname</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[197]"></a>u8x8_d_sh1106_128x64_vcomh0</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[198]"></a>u8x8_d_sh1106_128x64_winstar</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x64_noname.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_sh1106_generic
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[192]"></a>u8x8_d_helper_display_setup_memory</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_display.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_winstar
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_vcomh0
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_noname
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_alt0
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_vcomh0
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1312_128x64_noname
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_null_cb
</UL>

<P><STRONG><a name="[191]"></a>u8x8_d_helper_display_init</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_d_helper_display_init &rArr; u8x8_gpio_call
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_winstar
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_vcomh0
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_noname
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_alt0
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_vcomh0
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1312_128x64_noname
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_null_cb
</UL>

<P><STRONG><a name="[fb]"></a>u8x8_DrawTile</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateDisplayArea
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_tile_row
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_subglyph
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_subglyph
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawGlyph
</UL>

<P><STRONG><a name="[19e]"></a>u8x8_SetupMemory</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_SetupMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[2bc]"></a>u8x8_InitInterface</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, u8x8_display.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>u8x8_InitDisplay</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_InitDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_init
</UL>

<P><STRONG><a name="[8d]"></a>u8x8_SetPowerSave</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_SetPowerSave
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_init
</UL>

<P><STRONG><a name="[2bd]"></a>u8x8_SetFlipMode</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_display.o(.text), UNUSED)

<P><STRONG><a name="[11a]"></a>u8x8_SetContrast</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_display.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_init
</UL>

<P><STRONG><a name="[fd]"></a>u8x8_RefreshDisplay</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, u8x8_display.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_RefreshDisplay
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_NextPage
</UL>

<P><STRONG><a name="[19a]"></a>u8x8_ClearDisplayWithTile</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, u8x8_display.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_FillDisplay
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_ClearDisplay
</UL>

<P><STRONG><a name="[199]"></a>u8x8_ClearDisplay</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8x8_display.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_ClearDisplayWithTile
</UL>

<P><STRONG><a name="[19b]"></a>u8x8_FillDisplay</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, u8x8_display.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_ClearDisplayWithTile
</UL>

<P><STRONG><a name="[2be]"></a>u8x8_ClearLine</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, u8x8_display.o(.text), UNUSED)

<P><STRONG><a name="[166]"></a>u8x8_gpio_call</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, u8x8_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_4wire_sw_spi
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st7920_spi
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_sed1520
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_ks0108
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_set_ks0108_cs
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_3wire_sw_spi
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_8bit_8080mode
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_8bit_6800mode
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_sda
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_sda
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_scl
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>

<P><STRONG><a name="[19c]"></a>u8x8_dummy_cb</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, u8x8_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_dummy_cb
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8x8_setup.o(.text)
</UL>
<P><STRONG><a name="[13]"></a>u8x8_d_null_cb</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, u8x8_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_d_null_cb &rArr; u8x8_d_helper_display_init &rArr; u8x8_gpio_call
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.text)
</UL>
<P><STRONG><a name="[19d]"></a>u8x8_SetupDefaults</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, u8x8_setup.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
</UL>

<P><STRONG><a name="[116]"></a>u8x8_Setup</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, u8x8_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_Setup &rArr; u8x8_SetupMemory
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupDefaults
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetupMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_128x64_noname_f
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_null
</UL>

<P><STRONG><a name="[1a0]"></a>u8x8_u16toap</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, u8x8_u16toa.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_u16toa
</UL>

<P><STRONG><a name="[19f]"></a>u8x8_u16toa</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, u8x8_u16toa.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_u16toap
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utoa
</UL>

<P><STRONG><a name="[18f]"></a>u8x8_utoa</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, u8x8_u16toa.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_u16toa
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_xbm_pre
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_capture_write_pbm_pre
</UL>

<P><STRONG><a name="[1a1]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[2bf]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2c0]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[f9]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_ClearBuffer
</UL>

<P><STRONG><a name="[9d]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_Proc
</UL>

<P><STRONG><a name="[2c1]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[c0]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1a7]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1a8]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[bf]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ba]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_Proc
</UL>

<P><STRONG><a name="[c1]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[2c2]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1bf]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1aa]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1a3]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2c3]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[1a4]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[2c4]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[2c5]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[2c6]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[1a6]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>

<P><STRONG><a name="[1a5]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1ac]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1ad]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1bc]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[26]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[2c7]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2c8]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[2c9]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2ca]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1af]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2cb]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[78]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_Proc
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_Proc
</UL>

<P><STRONG><a name="[2cc]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[2cd]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1b1]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2ce]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[7e]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_ui
</UL>

<P><STRONG><a name="[2cf]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[2d0]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1b2]"></a>__0vsprintf</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2d1]"></a>__1vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[2d2]"></a>__2vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[2d3]"></a>__c89vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[ef]"></a>vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[1b4]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[e9]"></a>__hardfp_asinf</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, asinf.o(i.__hardfp_asinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_asinf &rArr; sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[e8]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[ea]"></a>__hardfp_cosf</STRONG> (Thumb, 280 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[eb]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[e5]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[1b6]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[1b9]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[1b8]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[1b5]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[1ba]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[2d4]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[2d5]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[2d6]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[1b7]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[1b3]"></a>sqrtf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, sqrtf.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[29]"></a>_ADCIntNumberGet</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ADCIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
</UL>

<P><STRONG><a name="[30]"></a>_GPIOIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
</UL>

<P><STRONG><a name="[56]"></a>_I2CIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
</UL>

<P><STRONG><a name="[7]"></a>_IntDefaultHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, interrupt.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> interrupt.o(.text)
</UL>
<P><STRONG><a name="[66]"></a>_SysCtlMemTimingGet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDeepSleepClockConfigSet
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockFreqSet
</UL>

<P><STRONG><a name="[67]"></a>_SysCtlFrequencyGet</STRONG> (Thumb, 96 bytes, Stack size 20 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockFreqSet
</UL>

<P><STRONG><a name="[6d]"></a>_TimerIntNumberGet</STRONG> (Thumb, 68 bytes, Stack size 20 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = _TimerIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
</UL>

<P><STRONG><a name="[73]"></a>_UARTIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _UARTIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
</UL>

<P><STRONG><a name="[3]"></a>NmiSR</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NmiSR
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NmiSR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>FaultISR</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FaultISR
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FaultISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>IntDefaultHandler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDefaultHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDefaultHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SycTickHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, systicktime.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> systicktime.o(.text)
</UL>
<P><STRONG><a name="[e7]"></a>compute_rotation_matrix</STRONG> (Thumb, 358 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[fa]"></a>u8g2_send_tile_row</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_buffer
</UL>

<P><STRONG><a name="[fc]"></a>u8g2_send_buffer</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, u8g2_buffer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = u8g2_send_buffer &rArr; u8g2_send_tile_row &rArr; u8x8_DrawTile
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_send_tile_row
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SendBuffer
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_UpdateDisplay
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_NextPage
</UL>

<P><STRONG><a name="[10b]"></a>u8g2_draw_circle_section</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle
</UL>

<P><STRONG><a name="[10d]"></a>u8g2_draw_circle</STRONG> (Thumb, 128 bytes, Stack size 64 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_circle_section
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawCircle
</UL>

<P><STRONG><a name="[10e]"></a>u8g2_draw_disc_section</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_disc
</UL>

<P><STRONG><a name="[10f]"></a>u8g2_draw_disc</STRONG> (Thumb, 128 bytes, Stack size 64 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_disc_section
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawDisc
</UL>

<P><STRONG><a name="[110]"></a>u8g2_draw_ellipse_section</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_ellipse
</UL>

<P><STRONG><a name="[111]"></a>u8g2_draw_ellipse</STRONG> (Thumb, 294 bytes, Stack size 72 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_ellipse_section
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawEllipse
</UL>

<P><STRONG><a name="[113]"></a>u8g2_draw_filled_ellipse_section</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawVLine
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_filled_ellipse
</UL>

<P><STRONG><a name="[114]"></a>u8g2_draw_filled_ellipse</STRONG> (Thumb, 294 bytes, Stack size 72 bytes, u8g2_circle.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_filled_ellipse_section
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawFilledEllipse
</UL>

<P><STRONG><a name="[11c]"></a>u8g2_font_get_byte</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_read_font_info
</UL>

<P><STRONG><a name="[11d]"></a>u8g2_font_get_word</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, u8g2_font.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetFontSize
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_read_font_info
</UL>

<P><STRONG><a name="[125]"></a>u8g2_font_setup_decode</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_font_setup_decode &rArr; u8g2_font_decode_get_unsigned_bits
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_unsigned_bits
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphHorizontalProperties
</UL>

<P><STRONG><a name="[129]"></a>u8g2_font_draw_glyph</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyph
</UL>

<P><STRONG><a name="[12a]"></a>u8g2_font_2x_draw_glyph</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_2x_decode_glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyphX2
</UL>

<P><STRONG><a name="[12f]"></a>u8g2_draw_string</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, u8g2_font.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = u8g2_draw_string &rArr; u8g2_DrawGlyph &rArr; u8g2_font_draw_glyph &rArr; u8g2_font_decode_glyph &rArr; u8g2_font_decode_len &rArr; u8g2_DrawHVLine &rArr; u8g2_clip_intersection2
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyph
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawStr
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawUTF8
</UL>

<P><STRONG><a name="[131]"></a>u8g2_draw_string_2x</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawGlyphX2
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawUTF8X2
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawStrX2
</UL>

<P><STRONG><a name="[141]"></a>u8g2_is_all_valid</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsAllValidUTF8
</UL>

<P><STRONG><a name="[143]"></a>u8g2_string_width</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetGlyphWidth
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetUTF8Width
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetStrWidth
</UL>

<P><STRONG><a name="[144]"></a>u8g2_GetGlyphHorizontalProperties</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, u8g2_font.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_get_glyph_data
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_decode_get_signed_bits
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_font_setup_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_GetStrX
</UL>

<P><STRONG><a name="[148]"></a>u8g2_clip_intersection2</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, u8g2_hvline.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_clip_intersection2
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_DrawHVLine
</UL>

<P><STRONG><a name="[14f]"></a>u8g2_update_dimension_common</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_update_dimension_common
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r3
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r2
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r1
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_dimension_r0
</UL>

<P><STRONG><a name="[14e]"></a>u8g2_apply_clip_window</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, u8g2_setup.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8g2_apply_clip_window &rArr; u8g2_IsIntersection &rArr; u8g2_is_intersection_decision_tree
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r3
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r2
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r1
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r0
</UL>

<P><STRONG><a name="[153]"></a>u8x8_get_glyph_data</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_1x2_subglyph
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_subglyph
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawGlyph
</UL>

<P><STRONG><a name="[156]"></a>u8x8_upscale_buf</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_draw_2x2_subglyph
</UL>

<P><STRONG><a name="[154]"></a>u8x8_draw_2x2_subglyph</STRONG> (Thumb, 172 bytes, Stack size 64 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_upscale_byte
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_upscale_buf
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_get_glyph_data
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw2x2Glyph
</UL>

<P><STRONG><a name="[158]"></a>u8x8_draw_1x2_subglyph</STRONG> (Thumb, 102 bytes, Stack size 64 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawTile
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_upscale_byte
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_get_glyph_data
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw1x2Glyph
</UL>

<P><STRONG><a name="[15a]"></a>u8x8_draw_string</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawGlyph
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawUTF8
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_DrawString
</UL>

<P><STRONG><a name="[15d]"></a>u8x8_draw_2x2_string</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw2x2Glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw2x2UTF8
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw2x2String
</UL>

<P><STRONG><a name="[160]"></a>u8x8_draw_1x2_string</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, u8x8_8x8.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_utf8_init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw1x2Glyph
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw1x2UTF8
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Draw1x2String
</UL>

<P><STRONG><a name="[16d]"></a>i2c_delay</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_bit
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
</UL>

<P><STRONG><a name="[16e]"></a>i2c_init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_sw_i2c
</UL>

<P><STRONG><a name="[16f]"></a>i2c_read_scl_and_delay</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_bit
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[170]"></a>i2c_clear_scl</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_bit
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[171]"></a>i2c_read_sda</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_bit
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[172]"></a>i2c_clear_sda</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[173]"></a>i2c_start</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_sda
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_sda
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_scl
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_sw_i2c
</UL>

<P><STRONG><a name="[174]"></a>i2c_stop</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_sda
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_sda
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_sw_i2c
</UL>

<P><STRONG><a name="[175]"></a>i2c_write_bit</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_sda
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_sda
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_scl
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_byte
</UL>

<P><STRONG><a name="[176]"></a>i2c_read_bit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_sda
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clear_scl
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_scl_and_delay
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_byte
</UL>

<P><STRONG><a name="[177]"></a>i2c_write_byte</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, u8x8_byte.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_bit
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_bit
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_sw_i2c
</UL>

<P><STRONG><a name="[188]"></a>u8x8_i2c_data_transfer</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, u8x8_cad.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_st75256_i2c
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_i2c
</UL>

<P><STRONG><a name="[190]"></a>u8x8_d_ssd1306_sh1106_generic</STRONG> (Thumb, 242 bytes, Stack size 32 bytes, u8x8_d_ssd1306_128x64_noname.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = u8x8_d_ssd1306_sh1106_generic &rArr; u8x8_cad_SendSequence &rArr; u8x8_cad_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_EndTransfer
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_StartTransfer
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendArg
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_noname
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_winstar
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_vcomh0
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_sh1106_128x64_noname
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_alt0
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x64_vcomh0
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1312_128x64_noname
</UL>

<P><STRONG><a name="[1bb]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1b0]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[1be]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1bd]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[15]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0sprintf)
<LI> printfa.o(i.__0vsprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
