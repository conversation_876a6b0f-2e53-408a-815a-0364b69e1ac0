--cpu=Cortex-M4.fp.sp
"..\..\output\adc.o"
"..\..\output\aes.o"
"..\..\output\can.o"
"..\..\output\comp.o"
"..\..\output\cpu.o"
"..\..\output\crc.o"
"..\..\output\des.o"
"..\..\output\eeprom.o"
"..\..\output\emac.o"
"..\..\output\epi.o"
"..\..\output\flash.o"
"..\..\output\fpu.o"
"..\..\output\gpio.o"
"..\..\output\hibernate.o"
"..\..\output\i2c.o"
"..\..\output\interrupt.o"
"..\..\output\lcd.o"
"..\..\output\mpu.o"
"..\..\output\pwm.o"
"..\..\output\qei.o"
"..\..\output\shamd5.o"
"..\..\output\ssi.o"
"..\..\output\sw_crc.o"
"..\..\output\sysctl.o"
"..\..\output\sysexc.o"
"..\..\output\systick.o"
"..\..\output\timer.o"
"..\..\output\uart.o"
"..\..\output\udma.o"
"..\..\output\usb.o"
"..\..\output\watchdog.o"
"..\..\output\main.o"
"..\..\output\uartstdio.o"
"..\..\output\startup_rvmdk.o"
"..\..\output\uart_app.o"
"..\..\output\scheduler.o"
"..\..\output\key_app.o"
"..\..\output\adc_app.o"
"..\..\output\myiic.o"
"..\..\output\icm20608.o"
"..\..\output\systicktime.o"
"..\..\output\imu.o"
"..\..\output\glcdfont.o"
"..\..\output\oled.o"
"..\..\output\ssd1306.o"
"..\..\output\oled_app.o"
"..\..\output\mui.o"
"..\..\output\mui_u8g2.o"
"..\..\output\u8g2_bitmap.o"
"..\..\output\u8g2_box.o"
"..\..\output\u8g2_buffer.o"
"..\..\output\u8g2_button.o"
"..\..\output\u8g2_circle.o"
"..\..\output\u8g2_cleardisplay.o"
"..\..\output\u8g2_d_memory.o"
"..\..\output\u8g2_d_setup.o"
"..\..\output\u8g2_font.o"
"..\..\output\u8g2_fonts.o"
"..\..\output\u8g2_hvline.o"
"..\..\output\u8g2_input_value.o"
"..\..\output\u8g2_intersection.o"
"..\..\output\u8g2_kerning.o"
"..\..\output\u8g2_line.o"
"..\..\output\u8g2_ll_hvline.o"
"..\..\output\u8g2_message.o"
"..\..\output\u8g2_polygon.o"
"..\..\output\u8g2_selection_list.o"
"..\..\output\u8g2_setup.o"
"..\..\output\u8log.o"
"..\..\output\u8log_u8g2.o"
"..\..\output\u8log_u8x8.o"
"..\..\output\u8x8_8x8.o"
"..\..\output\u8x8_byte.o"
"..\..\output\u8x8_cad.o"
"..\..\output\u8x8_capture.o"
"..\..\output\u8x8_d_ssd1306_128x64_noname.o"
"..\..\output\u8x8_debounce.o"
"..\..\output\u8x8_display.o"
"..\..\output\u8x8_fonts.o"
"..\..\output\u8x8_gpio.o"
"..\..\output\u8x8_input_value.o"
"..\..\output\u8x8_message.o"
"..\..\output\u8x8_selection_list.o"
"..\..\output\u8x8_setup.o"
"..\..\output\u8x8_string.o"
"..\..\output\u8x8_u8toa.o"
"..\..\output\u8x8_u16toa.o"
--library_type=microlib --strict --scatter "..\..\Output\TM4C123G.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\..\TM4C123G.map" -o ..\..\Output\TM4C123G.axf