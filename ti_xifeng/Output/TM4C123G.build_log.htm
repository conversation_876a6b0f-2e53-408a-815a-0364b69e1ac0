<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.41.0.0
Copyright (C) 2024 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 1 MECHREV, 1, LIC=21E4T-RQWR9-NNNAF-6GY2Y-PV1Y8-JP1FX
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.41.0.0
Toolchain Path:  C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 7 (build 960)
Assembler:       Armasm.exe V5.06 update 7 (build 960)
Linker/Locator:  ArmLink.exe V5.06 update 7 (build 960)
Library Manager: ArmAr.exe V5.06 update 7 (build 960)
Hex Converter:   FromElf.exe V5.06 update 7 (build 960)
CPU DLL:         SARMCM3.DLL V5.41.0.0
Dialog DLL:      DCM.DLL V1.17.5.0
Target DLL:      CMSIS_AGDI.dll V1.33.21.0
Dialog DLL:      TCM.DLL V1.56.4.0
 
<h2>Project:</h2>
C:\Users\<USER>\Desktop\ti_xifeng_9\ti_xifeng\Project\RVMDK��uv4��\TM4C123G.uvprojx
Project File Date:  07/30/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin'
Build target 'TIVA'
compiling scheduler.c...
compiling icm20608.c...
compiling main.c...
compiling uart_app.c...
compiling OLED.c...
compiling oled_app.c...
compiling SystickTime.c...
compiling key_app.c...
compiling adc_app.c...
compiling u8x8_byte.c...
linking...
Program Size: Code=53208 RO-data=18376 RW-data=2668 ZI-data=3396  
FromELF: creating hex file...
"..\..\Output\TM4C123G.axf" - 0 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: ARM
                https://www.keil.com/pack/ARM.CMSIS.6.1.0.pack
                ARM::CMSIS@6.1.0
                CMSIS (Common Microcontroller Software Interface Standard)
   * Component: CORE Version: 6.1.0

Package Vendor: Keil
                http://www.keil.com/pack/Keil.TM4C_DFP.1.1.0.pack
                Keil::TM4C_DFP@1.1.0
                Texas Instruments Tiva C Series Device Support and Examples

<h2>Collection of Component include folders:</h2>
  ./RTE/_TIVA
  C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include
  C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/TM4C_DFP/1.1.0/Device/Include/TM4C123

<h2>Collection of Component Files used:</h2>

   * Component: ARM::CMSIS:CORE@6.1.0
Build Time Elapsed:  00:00:02
</pre>
</body>
</html>
