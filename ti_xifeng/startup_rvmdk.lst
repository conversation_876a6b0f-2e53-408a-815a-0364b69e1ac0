


ARM Macro Assembler    Page 1 


    1 00000000         ; <<< Use Configuration Wizard in Context Menu >>>
    2 00000000         ;*******************************************************
                       ***********************
    3 00000000         ;
    4 00000000         ; startup_rvmdk.S - Startup code for use with <PERSON><PERSON>'s uVi
                       sion.
    5 00000000         ;
    6 00000000         ; Copyright (c) 2012-2013 Texas Instruments Incorporated
                       .  All rights reserved.
    7 00000000         ; Software License Agreement
    8 00000000         ; 
    9 00000000         ; Texas Instruments (TI) is supplying this software for 
                       use solely and
   10 00000000         ; exclusively on TI's microcontroller products. The soft
                       ware is owned by
   11 00000000         ; TI and/or its suppliers, and is protected under applic
                       able copyright
   12 00000000         ; laws. You may not combine this software with "viral" o
                       pen-source
   13 00000000         ; software in order to form a larger program.
   14 00000000         ; 
   15 00000000         ; THIS SOFTWARE IS PROVIDED "AS IS" AND WITH ALL FAULTS.
                       
   16 00000000         ; NO WARRANTIES, WHETHER EXPRESS, IMPLIED OR STATUTORY, 
                       INCLUDING, BUT
   17 00000000         ; NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY 
                       AND FITNESS FOR
   18 00000000         ; A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE. TI SHALL 
                       NOT, UNDER ANY
   19 00000000         ; CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL, OR C
                       ONSEQUENTIAL
   20 00000000         ; DAMAGES, FOR ANY REASON WHATSOEVER.
   21 00000000         ; 
   22 00000000         ; This is part of revision 2.0.1.11577 of the EK-TM4C123
                       GXL Firmware Package.
   23 00000000         ;
   24 00000000         ;*******************************************************
                       ***********************
   25 00000000         
   26 00000000         ;*******************************************************
                       ***********************
   27 00000000         ;
   28 00000000         ; <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   29 00000000         ;
   30 00000000         ;*******************************************************
                       ***********************
   31 00000000 00000200 
                       Stack   EQU              0x00000200
   32 00000000         
   33 00000000         ;*******************************************************
                       ***********************
   34 00000000         ;
   35 00000000         ; <o> Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   36 00000000         ;
   37 00000000         ;*******************************************************
                       ***********************
   38 00000000 00000000 
                       Heap    EQU              0x00000000
   39 00000000         



ARM Macro Assembler    Page 2 


   40 00000000         ;*******************************************************
                       ***********************
   41 00000000         ;
   42 00000000         ; Allocate space for the stack.
   43 00000000         ;
   44 00000000         ;*******************************************************
                       ***********************
   45 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   46 00000000         StackMem
   47 00000000                 SPACE            Stack
   48 00000200         __initial_sp
   49 00000200         
   50 00000200         ;*******************************************************
                       ***********************
   51 00000200         ;
   52 00000200         ; Allocate space for the heap.
   53 00000200         ;
   54 00000200         ;*******************************************************
                       ***********************
   55 00000200                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   56 00000000         __heap_base
   57 00000000         HeapMem
   58 00000000                 SPACE            Heap
   59 00000000         __heap_limit
   60 00000000         
   61 00000000         ;*******************************************************
                       ***********************
   62 00000000         ;
   63 00000000         ; Indicate that the code in this file preserves 8-byte a
                       lignment of the stack.
   64 00000000         ;
   65 00000000         ;*******************************************************
                       ***********************
   66 00000000                 PRESERVE8
   67 00000000         
   68 00000000         ;*******************************************************
                       ***********************
   69 00000000         ;
   70 00000000         ; Place code into the reset code section.
   71 00000000         ;
   72 00000000         ;*******************************************************
                       ***********************
   73 00000000                 AREA             RESET, CODE, READONLY
   74 00000000                 THUMB
   75 00000000         
   76 00000000         ;*******************************************************
                       ***********************
   77 00000000         ;
   78 00000000         ; The vector table.
   79 00000000         ;
   80 00000000         ;*******************************************************
                       ***********************
   81 00000000                 EXPORT           __Vectors
   82 00000000         __Vectors
   83 00000000 00000200        DCD              StackMem + Stack ; Top of Stack
                                                            
   84 00000004 00000000        DCD              Reset_Handler ; Reset Handler



ARM Macro Assembler    Page 3 


   85 00000008 00000000        DCD              NmiSR       ; NMI Handler
   86 0000000C 00000000        DCD              FaultISR    ; Hard Fault Handle
                                                            r
   87 00000010 00000000        DCD              IntDefaultHandler ; The MPU fau
                                                            lt handler
   88 00000014 00000000        DCD              IntDefaultHandler ; The bus fau
                                                            lt handler
   89 00000018 00000000        DCD              IntDefaultHandler ; The usage f
                                                            ault handler
   90 0000001C 00000000        DCD              0           ; Reserved
   91 00000020 00000000        DCD              0           ; Reserved
   92 00000024 00000000        DCD              0           ; Reserved
   93 00000028 00000000        DCD              0           ; Reserved
   94 0000002C 00000000        DCD              IntDefaultHandler 
                                                            ; SVCall handler
   95 00000030 00000000        DCD              IntDefaultHandler ; Debug monit
                                                            or handler
   96 00000034 00000000        DCD              0           ; Reserved
   97 00000038 00000000        DCD              IntDefaultHandler ; The PendSV 
                                                            handler
   98 0000003C 00000000        DCD              IntDefaultHandler ; The SysTick
                                                             handler
   99 00000040 00000000        DCD              IntDefaultHandler ; GPIO Port A
                                                            
  100 00000044 00000000        DCD              IntDefaultHandler ; GPIO Port B
                                                            
  101 00000048 00000000        DCD              IntDefaultHandler ; GPIO Port C
                                                            
  102 0000004C 00000000        DCD              IntDefaultHandler ; GPIO Port D
                                                            
  103 00000050 00000000        DCD              IntDefaultHandler ; GPIO Port E
                                                            
  104 00000054 00000000        DCD              IntDefaultHandler 
                                                            ; UART0 Rx and Tx
  105 00000058 00000000        DCD              IntDefaultHandler 
                                                            ; UART1 Rx and Tx
  106 0000005C 00000000        DCD              IntDefaultHandler 
                                                            ; SSI0 Rx and Tx
  107 00000060 00000000        DCD              IntDefaultHandler ; I2C0 Master
                                                             and Slave
  108 00000064 00000000        DCD              IntDefaultHandler ; PWM Fault
  109 00000068 00000000        DCD              IntDefaultHandler 
                                                            ; PWM Generator 0
  110 0000006C 00000000        DCD              IntDefaultHandler 
                                                            ; PWM Generator 1
  111 00000070 00000000        DCD              IntDefaultHandler 
                                                            ; PWM Generator 2
  112 00000074 00000000        DCD              IntDefaultHandler ; Quadrature 
                                                            Encoder 0
  113 00000078 00000000        DCD              IntDefaultHandler 
                                                            ; ADC Sequence 0
  114 0000007C 00000000        DCD              IntDefaultHandler 
                                                            ; ADC Sequence 1
  115 00000080 00000000        DCD              IntDefaultHandler 
                                                            ; ADC Sequence 2
  116 00000084 00000000        DCD              IntDefaultHandler 
                                                            ; ADC Sequence 3
  117 00000088 00000000        DCD              IntDefaultHandler 
                                                            ; Watchdog timer



ARM Macro Assembler    Page 4 


  118 0000008C 00000000        DCD              IntDefaultHandler ; Timer 0 sub
                                                            timer A
  119 00000090 00000000        DCD              IntDefaultHandler ; Timer 0 sub
                                                            timer B
  120 00000094 00000000        DCD              IntDefaultHandler ; Timer 1 sub
                                                            timer A
  121 00000098 00000000        DCD              IntDefaultHandler ; Timer 1 sub
                                                            timer B
  122 0000009C 00000000        DCD              IntDefaultHandler ; Timer 2 sub
                                                            timer A
  123 000000A0 00000000        DCD              IntDefaultHandler ; Timer 2 sub
                                                            timer B
  124 000000A4 00000000        DCD              IntDefaultHandler ; Analog Comp
                                                            arator 0
  125 000000A8 00000000        DCD              IntDefaultHandler ; Analog Comp
                                                            arator 1
  126 000000AC 00000000        DCD              IntDefaultHandler ; Analog Comp
                                                            arator 2
  127 000000B0 00000000        DCD              IntDefaultHandler ; System Cont
                                                            rol (PLL, OSC, BO)
  128 000000B4 00000000        DCD              IntDefaultHandler 
                                                            ; FLASH Control
  129 000000B8 00000000        DCD              IntDefaultHandler ; GPIO Port F
                                                            
  130 000000BC 00000000        DCD              IntDefaultHandler ; GPIO Port G
                                                            
  131 000000C0 00000000        DCD              IntDefaultHandler ; GPIO Port H
                                                            
  132 000000C4 00000000        DCD              IntDefaultHandler 
                                                            ; UART2 Rx and Tx
  133 000000C8 00000000        DCD              IntDefaultHandler 
                                                            ; SSI1 Rx and Tx
  134 000000CC 00000000        DCD              IntDefaultHandler ; Timer 3 sub
                                                            timer A
  135 000000D0 00000000        DCD              IntDefaultHandler ; Timer 3 sub
                                                            timer B
  136 000000D4 00000000        DCD              IntDefaultHandler ; I2C1 Master
                                                             and Slave
  137 000000D8 00000000        DCD              IntDefaultHandler ; Quadrature 
                                                            Encoder 1
  138 000000DC 00000000        DCD              IntDefaultHandler ; CAN0
  139 000000E0 00000000        DCD              IntDefaultHandler ; CAN1
  140 000000E4 00000000        DCD              IntDefaultHandler ; CAN2
  141 000000E8 00000000        DCD              0           ; Reserved
  142 000000EC 00000000        DCD              IntDefaultHandler ; Hibernate
  143 000000F0 00000000        DCD              IntDefaultHandler ; USB0
  144 000000F4 00000000        DCD              IntDefaultHandler 
                                                            ; PWM Generator 3
  145 000000F8 00000000        DCD              IntDefaultHandler ; uDMA Softwa
                                                            re Transfer
  146 000000FC 00000000        DCD              IntDefaultHandler ; uDMA Error
  147 00000100 00000000        DCD              IntDefaultHandler 
                                                            ; ADC1 Sequence 0
  148 00000104 00000000        DCD              IntDefaultHandler 
                                                            ; ADC1 Sequence 1
  149 00000108 00000000        DCD              IntDefaultHandler 
                                                            ; ADC1 Sequence 2
  150 0000010C 00000000        DCD              IntDefaultHandler 
                                                            ; ADC1 Sequence 3



ARM Macro Assembler    Page 5 


  151 00000110 00000000        DCD              0           ; Reserved
  152 00000114 00000000        DCD              0           ; Reserved
  153 00000118 00000000        DCD              IntDefaultHandler ; GPIO Port J
                                                            
  154 0000011C 00000000        DCD              IntDefaultHandler ; GPIO Port K
                                                            
  155 00000120 00000000        DCD              IntDefaultHandler ; GPIO Port L
                                                            
  156 00000124 00000000        DCD              IntDefaultHandler 
                                                            ; SSI2 Rx and Tx
  157 00000128 00000000        DCD              IntDefaultHandler 
                                                            ; SSI3 Rx and Tx
  158 0000012C 00000000        DCD              IntDefaultHandler 
                                                            ; UART3 Rx and Tx
  159 00000130 00000000        DCD              IntDefaultHandler 
                                                            ; UART4 Rx and Tx
  160 00000134 00000000        DCD              IntDefaultHandler 
                                                            ; UART5 Rx and Tx
  161 00000138 00000000        DCD              IntDefaultHandler 
                                                            ; UART6 Rx and Tx
  162 0000013C 00000000        DCD              IntDefaultHandler 
                                                            ; UART7 Rx and Tx
  163 00000140 00000000        DCD              0           ; Reserved
  164 00000144 00000000        DCD              0           ; Reserved
  165 00000148 00000000        DCD              0           ; Reserved
  166 0000014C 00000000        DCD              0           ; Reserved
  167 00000150 00000000        DCD              IntDefaultHandler ; I2C2 Master
                                                             and Slave
  168 00000154 00000000        DCD              IntDefaultHandler ; I2C3 Master
                                                             and Slave
  169 00000158 00000000        DCD              IntDefaultHandler ; Timer 4 sub
                                                            timer A
  170 0000015C 00000000        DCD              IntDefaultHandler ; Timer 4 sub
                                                            timer B
  171 00000160 00000000        DCD              0           ; Reserved
  172 00000164 00000000        DCD              0           ; Reserved
  173 00000168 00000000        DCD              0           ; Reserved
  174 0000016C 00000000        DCD              0           ; Reserved
  175 00000170 00000000        DCD              0           ; Reserved
  176 00000174 00000000        DCD              0           ; Reserved
  177 00000178 00000000        DCD              0           ; Reserved
  178 0000017C 00000000        DCD              0           ; Reserved
  179 00000180 00000000        DCD              0           ; Reserved
  180 00000184 00000000        DCD              0           ; Reserved
  181 00000188 00000000        DCD              0           ; Reserved
  182 0000018C 00000000        DCD              0           ; Reserved
  183 00000190 00000000        DCD              0           ; Reserved
  184 00000194 00000000        DCD              0           ; Reserved
  185 00000198 00000000        DCD              0           ; Reserved
  186 0000019C 00000000        DCD              0           ; Reserved
  187 000001A0 00000000        DCD              0           ; Reserved
  188 000001A4 00000000        DCD              0           ; Reserved
  189 000001A8 00000000        DCD              0           ; Reserved
  190 000001AC 00000000        DCD              0           ; Reserved
  191 000001B0 00000000        DCD              IntDefaultHandler ; Timer 5 sub
                                                            timer A
  192 000001B4 00000000        DCD              IntDefaultHandler ; Timer 5 sub
                                                            timer B
  193 000001B8 00000000        DCD              IntDefaultHandler ; Wide Timer 



ARM Macro Assembler    Page 6 


                                                            0 subtimer A
  194 000001BC 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            0 subtimer B
  195 000001C0 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            1 subtimer A
  196 000001C4 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            1 subtimer B
  197 000001C8 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            2 subtimer A
  198 000001CC 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            2 subtimer B
  199 000001D0 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            3 subtimer A
  200 000001D4 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            3 subtimer B
  201 000001D8 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            4 subtimer A
  202 000001DC 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            4 subtimer B
  203 000001E0 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            5 subtimer A
  204 000001E4 00000000        DCD              IntDefaultHandler ; Wide Timer 
                                                            5 subtimer B
  205 000001E8 00000000        DCD              IntDefaultHandler ; FPU
  206 000001EC 00000000        DCD              0           ; Reserved
  207 000001F0 00000000        DCD              0           ; Reserved
  208 000001F4 00000000        DCD              IntDefaultHandler ; I2C4 Master
                                                             and Slave
  209 000001F8 00000000        DCD              IntDefaultHandler ; I2C5 Master
                                                             and Slave
  210 000001FC 00000000        DCD              IntDefaultHandler ; GPIO Port M
                                                            
  211 00000200 00000000        DCD              IntDefaultHandler ; GPIO Port N
                                                            
  212 00000204 00000000        DCD              IntDefaultHandler ; Quadrature 
                                                            Encoder 2
  213 00000208 00000000        DCD              0           ; Reserved
  214 0000020C 00000000        DCD              0           ; Reserved
  215 00000210 00000000        DCD              IntDefaultHandler ; GPIO Port P
                                                             (Summary or P0)
  216 00000214 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P1
  217 00000218 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P2
  218 0000021C 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P3
  219 00000220 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P4
  220 00000224 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P5
  221 00000228 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P6
  222 0000022C 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port P7
  223 00000230 00000000        DCD              IntDefaultHandler ; GPIO Port Q
                                                             (Summary or Q0)
  224 00000234 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q1
  225 00000238 00000000        DCD              IntDefaultHandler 



ARM Macro Assembler    Page 7 


                                                            ; GPIO Port Q2
  226 0000023C 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q3
  227 00000240 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q4
  228 00000244 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q5
  229 00000248 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q6
  230 0000024C 00000000        DCD              IntDefaultHandler 
                                                            ; GPIO Port Q7
  231 00000250 00000000        DCD              IntDefaultHandler ; GPIO Port R
                                                            
  232 00000254 00000000        DCD              IntDefaultHandler ; GPIO Port S
                                                            
  233 00000258 00000000        DCD              IntDefaultHandler 
                                                            ; PWM 1 Generator 0
                                                            
  234 0000025C 00000000        DCD              IntDefaultHandler 
                                                            ; PWM 1 Generator 1
                                                            
  235 00000260 00000000        DCD              IntDefaultHandler 
                                                            ; PWM 1 Generator 2
                                                            
  236 00000264 00000000        DCD              IntDefaultHandler 
                                                            ; PWM 1 Generator 3
                                                            
  237 00000268 00000000        DCD              IntDefaultHandler ; PWM 1 Fault
                                                            
  238 0000026C         
  239 0000026C         ;*******************************************************
                       ***********************
  240 0000026C         ;
  241 0000026C         ; This is the code that gets called when the processor f
                       irst starts execution
  242 0000026C         ; following a reset event.
  243 0000026C         ;
  244 0000026C         ;*******************************************************
                       ***********************
  245 0000026C                 EXPORT           Reset_Handler
  246 0000026C         Reset_Handler
  247 0000026C         ;
  248 0000026C         ; Enable the floating-point unit.  This must be done her
                       e to handle the
  249 0000026C         ; case where main() uses floating-point and the function
                        prologue saves
  250 0000026C         ; floating-point registers (which will fault if floating
                       -point is not
  251 0000026C         ; enabled).  Any configuration of the floating-point uni
                       t using
  252 0000026C         ; DriverLib APIs must be done here prior to the floating
                       -point unit
  253 0000026C         ; being enabled.
  254 0000026C         ;
  255 0000026C         ; Note that this does not use DriverLib since it might n
                       ot be included
  256 0000026C         ; in this project.
  257 0000026C         ;
  258 0000026C F64E 5088       MOVW             R0, #0xED88



ARM Macro Assembler    Page 8 


  259 00000270 F2CE 0000       MOVT             R0, #0xE000
  260 00000274 6801            LDR              R1, [R0]
  261 00000276 F441 0170       ORR              R1, #0x00F00000
  262 0000027A 6001            STR              R1, [R0]
  263 0000027C         
  264 0000027C         ;
  265 0000027C         ; Call the C library enty point that handles startup.  T
                       his will copy
  266 0000027C         ; the .data section initializers from flash to SRAM and 
                       zero fill the
  267 0000027C         ; .bss section.
  268 0000027C         ;
  269 0000027C                 IMPORT           __main
  270 0000027C F7FF BFFE       B                __main
  271 00000280         
  272 00000280         ;*******************************************************
                       ***********************
  273 00000280         ;
  274 00000280         ; This is the code that gets called when the processor r
                       eceives a NMI.  This
  275 00000280         ; simply enters an infinite loop, preserving the system 
                       state for examination
  276 00000280         ; by a debugger.
  277 00000280         ;
  278 00000280         ;*******************************************************
                       ***********************
  279 00000280         NmiSR
  280 00000280 E7FE            B                NmiSR
  281 00000282         
  282 00000282         ;*******************************************************
                       ***********************
  283 00000282         ;
  284 00000282         ; This is the code that gets called when the processor r
                       eceives a fault
  285 00000282         ; interrupt.  This simply enters an infinite loop, prese
                       rving the system state
  286 00000282         ; for examination by a debugger.
  287 00000282         ;
  288 00000282         ;*******************************************************
                       ***********************
  289 00000282         FaultISR
  290 00000282 E7FE            B                FaultISR
  291 00000284         
  292 00000284         ;*******************************************************
                       ***********************
  293 00000284         ;
  294 00000284         ; This is the code that gets called when the processor r
                       eceives an unexpected
  295 00000284         ; interrupt.  This simply enters an infinite loop, prese
                       rving the system state
  296 00000284         ; for examination by a debugger.
  297 00000284         ;
  298 00000284         ;*******************************************************
                       ***********************
  299 00000284         IntDefaultHandler
  300 00000284 E7FE            B                IntDefaultHandler
  301 00000286         
  302 00000286         ;*******************************************************
                       ***********************



ARM Macro Assembler    Page 9 


  303 00000286         ;
  304 00000286         ; Make sure the end of this section is aligned.
  305 00000286         ;
  306 00000286         ;*******************************************************
                       ***********************
  307 00000286 00 00           ALIGN
  308 00000288         
  309 00000288         ;*******************************************************
                       ***********************
  310 00000288         ;
  311 00000288         ; Some code in the normal code section for initializing 
                       the heap and stack.
  312 00000288         ;
  313 00000288         ;*******************************************************
                       ***********************
  314 00000288                 AREA             |.text|, CODE, READONLY
  315 00000000         
  316 00000000         ;*******************************************************
                       ***********************
  317 00000000         ;
  318 00000000         ; The function expected of the C library startup code fo
                       r defining the stack
  319 00000000         ; and heap memory locations.  For the C library version 
                       of the startup code,
  320 00000000         ; provide this function so that the C library initializa
                       tion code can find out
  321 00000000         ; the location of the stack and heap.
  322 00000000         ;
  323 00000000         ;*******************************************************
                       ***********************
  324 00000000                 IF               :DEF: __MICROLIB
  325 00000000                 EXPORT           __initial_sp
  326 00000000                 EXPORT           __heap_base
  327 00000000                 EXPORT           __heap_limit
  328 00000000                 ELSE
  337                          ENDIF
  338 00000000         
  339 00000000         ;*******************************************************
                       ***********************
  340 00000000         ;
  341 00000000         ; Make sure the end of this section is aligned.
  342 00000000         ;
  343 00000000         ;*******************************************************
                       ***********************
  344 00000000                 ALIGN
  345 00000000         
  346 00000000         ;*******************************************************
                       ***********************
  347 00000000         ;
  348 00000000         ; Tell the assembler that we're done.
  349 00000000         ;
  350 00000000         ;*******************************************************
                       ***********************
  351 00000000                 END
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=..\..\output\startup_rvmdk.d -o..\..\output\startup_rvmdk.o 
-I.\RTE\_TIVA -IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\
Core\Include -IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\TM4C_DFP\1.1.0\Dev
ice\Include\TM4C123 --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERS



ARM Macro Assembler    Page 10 


ION SETA 541" --predefine="TM4C123GH6PZ SETA 1" --predefine="_RTE_ SETA 1" --li
st=..\..\startup_rvmdk.lst ..\..\User\startup_rvmdk.S



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 45 in file ..\..\User\startup_rvmdk.S
   Uses
      None
Comment: STACK unused
StackMem 00000000

Symbol: StackMem
   Definitions
      At line 46 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 83 in file ..\..\User\startup_rvmdk.S
Comment: StackMem used once
__initial_sp 00000200

Symbol: __initial_sp
   Definitions
      At line 48 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 325 in file ..\..\User\startup_rvmdk.S
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 55 in file ..\..\User\startup_rvmdk.S
   Uses
      None
Comment: HEAP unused
HeapMem 00000000

Symbol: HeapMem
   Definitions
      At line 57 in file ..\..\User\startup_rvmdk.S
   Uses
      None
Comment: HeapMem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 56 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 326 in file ..\..\User\startup_rvmdk.S
Comment: __heap_base used once
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 59 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 327 in file ..\..\User\startup_rvmdk.S
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

FaultISR 00000282

Symbol: FaultISR
   Definitions
      At line 289 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 86 in file ..\..\User\startup_rvmdk.S
      At line 290 in file ..\..\User\startup_rvmdk.S

IntDefaultHandler 00000284

Symbol: IntDefaultHandler
   Definitions
      At line 299 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 87 in file ..\..\User\startup_rvmdk.S
      At line 88 in file ..\..\User\startup_rvmdk.S
      At line 89 in file ..\..\User\startup_rvmdk.S
      At line 94 in file ..\..\User\startup_rvmdk.S
      At line 95 in file ..\..\User\startup_rvmdk.S
      At line 97 in file ..\..\User\startup_rvmdk.S
      At line 98 in file ..\..\User\startup_rvmdk.S
      At line 99 in file ..\..\User\startup_rvmdk.S
      At line 100 in file ..\..\User\startup_rvmdk.S
      At line 101 in file ..\..\User\startup_rvmdk.S
      At line 102 in file ..\..\User\startup_rvmdk.S
      At line 103 in file ..\..\User\startup_rvmdk.S
      At line 104 in file ..\..\User\startup_rvmdk.S
      At line 105 in file ..\..\User\startup_rvmdk.S
      At line 106 in file ..\..\User\startup_rvmdk.S
      At line 107 in file ..\..\User\startup_rvmdk.S
      At line 108 in file ..\..\User\startup_rvmdk.S
      At line 109 in file ..\..\User\startup_rvmdk.S
      At line 110 in file ..\..\User\startup_rvmdk.S
      At line 111 in file ..\..\User\startup_rvmdk.S
      At line 112 in file ..\..\User\startup_rvmdk.S
      At line 113 in file ..\..\User\startup_rvmdk.S
      At line 114 in file ..\..\User\startup_rvmdk.S
      At line 115 in file ..\..\User\startup_rvmdk.S
      At line 116 in file ..\..\User\startup_rvmdk.S
      At line 117 in file ..\..\User\startup_rvmdk.S
      At line 118 in file ..\..\User\startup_rvmdk.S
      At line 119 in file ..\..\User\startup_rvmdk.S
      At line 120 in file ..\..\User\startup_rvmdk.S
      At line 121 in file ..\..\User\startup_rvmdk.S
      At line 122 in file ..\..\User\startup_rvmdk.S
      At line 123 in file ..\..\User\startup_rvmdk.S
      At line 124 in file ..\..\User\startup_rvmdk.S
      At line 125 in file ..\..\User\startup_rvmdk.S
      At line 126 in file ..\..\User\startup_rvmdk.S
      At line 127 in file ..\..\User\startup_rvmdk.S
      At line 128 in file ..\..\User\startup_rvmdk.S
      At line 129 in file ..\..\User\startup_rvmdk.S
      At line 130 in file ..\..\User\startup_rvmdk.S
      At line 131 in file ..\..\User\startup_rvmdk.S
      At line 132 in file ..\..\User\startup_rvmdk.S
      At line 133 in file ..\..\User\startup_rvmdk.S
      At line 134 in file ..\..\User\startup_rvmdk.S
      At line 135 in file ..\..\User\startup_rvmdk.S



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 136 in file ..\..\User\startup_rvmdk.S
      At line 137 in file ..\..\User\startup_rvmdk.S
      At line 138 in file ..\..\User\startup_rvmdk.S
      At line 139 in file ..\..\User\startup_rvmdk.S
      At line 140 in file ..\..\User\startup_rvmdk.S
      At line 142 in file ..\..\User\startup_rvmdk.S
      At line 143 in file ..\..\User\startup_rvmdk.S
      At line 144 in file ..\..\User\startup_rvmdk.S
      At line 145 in file ..\..\User\startup_rvmdk.S
      At line 146 in file ..\..\User\startup_rvmdk.S
      At line 147 in file ..\..\User\startup_rvmdk.S
      At line 148 in file ..\..\User\startup_rvmdk.S
      At line 149 in file ..\..\User\startup_rvmdk.S
      At line 150 in file ..\..\User\startup_rvmdk.S
      At line 153 in file ..\..\User\startup_rvmdk.S
      At line 154 in file ..\..\User\startup_rvmdk.S
      At line 155 in file ..\..\User\startup_rvmdk.S
      At line 156 in file ..\..\User\startup_rvmdk.S
      At line 157 in file ..\..\User\startup_rvmdk.S
      At line 158 in file ..\..\User\startup_rvmdk.S
      At line 159 in file ..\..\User\startup_rvmdk.S
      At line 160 in file ..\..\User\startup_rvmdk.S
      At line 161 in file ..\..\User\startup_rvmdk.S
      At line 162 in file ..\..\User\startup_rvmdk.S
      At line 167 in file ..\..\User\startup_rvmdk.S
      At line 168 in file ..\..\User\startup_rvmdk.S
      At line 169 in file ..\..\User\startup_rvmdk.S
      At line 170 in file ..\..\User\startup_rvmdk.S
      At line 191 in file ..\..\User\startup_rvmdk.S
      At line 192 in file ..\..\User\startup_rvmdk.S
      At line 193 in file ..\..\User\startup_rvmdk.S
      At line 194 in file ..\..\User\startup_rvmdk.S
      At line 195 in file ..\..\User\startup_rvmdk.S
      At line 196 in file ..\..\User\startup_rvmdk.S
      At line 197 in file ..\..\User\startup_rvmdk.S
      At line 198 in file ..\..\User\startup_rvmdk.S
      At line 199 in file ..\..\User\startup_rvmdk.S
      At line 200 in file ..\..\User\startup_rvmdk.S
      At line 201 in file ..\..\User\startup_rvmdk.S
      At line 202 in file ..\..\User\startup_rvmdk.S
      At line 203 in file ..\..\User\startup_rvmdk.S
      At line 204 in file ..\..\User\startup_rvmdk.S
      At line 205 in file ..\..\User\startup_rvmdk.S
      At line 208 in file ..\..\User\startup_rvmdk.S
      At line 209 in file ..\..\User\startup_rvmdk.S
      At line 210 in file ..\..\User\startup_rvmdk.S
      At line 211 in file ..\..\User\startup_rvmdk.S
      At line 212 in file ..\..\User\startup_rvmdk.S
      At line 215 in file ..\..\User\startup_rvmdk.S
      At line 216 in file ..\..\User\startup_rvmdk.S
      At line 217 in file ..\..\User\startup_rvmdk.S
      At line 218 in file ..\..\User\startup_rvmdk.S
      At line 219 in file ..\..\User\startup_rvmdk.S
      At line 220 in file ..\..\User\startup_rvmdk.S
      At line 221 in file ..\..\User\startup_rvmdk.S
      At line 222 in file ..\..\User\startup_rvmdk.S
      At line 223 in file ..\..\User\startup_rvmdk.S
      At line 224 in file ..\..\User\startup_rvmdk.S
      At line 225 in file ..\..\User\startup_rvmdk.S



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

      At line 226 in file ..\..\User\startup_rvmdk.S
      At line 227 in file ..\..\User\startup_rvmdk.S
      At line 228 in file ..\..\User\startup_rvmdk.S
      At line 229 in file ..\..\User\startup_rvmdk.S
      At line 230 in file ..\..\User\startup_rvmdk.S
      At line 231 in file ..\..\User\startup_rvmdk.S
      At line 232 in file ..\..\User\startup_rvmdk.S
      At line 233 in file ..\..\User\startup_rvmdk.S
      At line 234 in file ..\..\User\startup_rvmdk.S
      At line 235 in file ..\..\User\startup_rvmdk.S
      At line 236 in file ..\..\User\startup_rvmdk.S
      At line 237 in file ..\..\User\startup_rvmdk.S
      At line 300 in file ..\..\User\startup_rvmdk.S

NmiSR 00000280

Symbol: NmiSR
   Definitions
      At line 279 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 85 in file ..\..\User\startup_rvmdk.S
      At line 280 in file ..\..\User\startup_rvmdk.S

RESET 00000000

Symbol: RESET
   Definitions
      At line 73 in file ..\..\User\startup_rvmdk.S
   Uses
      None
Comment: RESET unused
Reset_Handler 0000026C

Symbol: Reset_Handler
   Definitions
      At line 246 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 84 in file ..\..\User\startup_rvmdk.S
      At line 245 in file ..\..\User\startup_rvmdk.S

__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 82 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 81 in file ..\..\User\startup_rvmdk.S
Comment: __Vectors used once
6 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 314 in file ..\..\User\startup_rvmdk.S
   Uses
      None
Comment: .text unused
1 symbol



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap 00000000

Symbol: Heap
   Definitions
      At line 38 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 58 in file ..\..\User\startup_rvmdk.S
Comment: Heap used once
Stack 00000200

Symbol: Stack
   Definitions
      At line 31 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 47 in file ..\..\User\startup_rvmdk.S
      At line 83 in file ..\..\User\startup_rvmdk.S

2 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

__main 00000000

Symbol: __main
   Definitions
      At line 269 in file ..\..\User\startup_rvmdk.S
   Uses
      At line 270 in file ..\..\User\startup_rvmdk.S
Comment: __main used once
1 symbol
352 symbols in table
