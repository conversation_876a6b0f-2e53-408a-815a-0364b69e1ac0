<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>driverlib</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>adc.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/adc.c</locationURI>
		</link>
		<link>
			<name>aes.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/aes.c</locationURI>
		</link>
		<link>
			<name>can.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/can.c</locationURI>
		</link>
		<link>
			<name>comp.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/comp.c</locationURI>
		</link>
		<link>
			<name>cpu.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/cpu.c</locationURI>
		</link>
		<link>
			<name>crc.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/crc.c</locationURI>
		</link>
		<link>
			<name>des.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/des.c</locationURI>
		</link>
		<link>
			<name>eeprom.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/eeprom.c</locationURI>
		</link>
		<link>
			<name>emac.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/emac.c</locationURI>
		</link>
		<link>
			<name>epi.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/epi.c</locationURI>
		</link>
		<link>
			<name>epi_workaround_ccs.s</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/epi_workaround_ccs.s</locationURI>
		</link>
		<link>
			<name>flash.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/flash.c</locationURI>
		</link>
		<link>
			<name>fpu.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/fpu.c</locationURI>
		</link>
		<link>
			<name>gpio.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/gpio.c</locationURI>
		</link>
		<link>
			<name>hibernate.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/hibernate.c</locationURI>
		</link>
		<link>
			<name>i2c.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/i2c.c</locationURI>
		</link>
		<link>
			<name>interrupt.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/interrupt.c</locationURI>
		</link>
		<link>
			<name>lcd.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/lcd.c</locationURI>
		</link>
		<link>
			<name>mpu.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/mpu.c</locationURI>
		</link>
		<link>
			<name>pwm.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/pwm.c</locationURI>
		</link>
		<link>
			<name>qei.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/qei.c</locationURI>
		</link>
		<link>
			<name>shamd5.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/shamd5.c</locationURI>
		</link>
		<link>
			<name>ssi.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/ssi.c</locationURI>
		</link>
		<link>
			<name>sw_crc.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/sw_crc.c</locationURI>
		</link>
		<link>
			<name>sysctl.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/sysctl.c</locationURI>
		</link>
		<link>
			<name>sysexc.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/sysexc.c</locationURI>
		</link>
		<link>
			<name>systick.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/systick.c</locationURI>
		</link>
		<link>
			<name>timer.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/timer.c</locationURI>
		</link>
		<link>
			<name>uart.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/uart.c</locationURI>
		</link>
		<link>
			<name>udma.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/udma.c</locationURI>
		</link>
		<link>
			<name>usb.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/usb.c</locationURI>
		</link>
		<link>
			<name>watchdog.c</name>
			<type>1</type>
			<locationURI>SW_ROOT/driverlib/watchdog.c</locationURI>
		</link>
	</linkedResources>
	<variableList>
		<variable>
			<name>SW_ROOT</name>
			<value>$%7BPARENT-2-PROJECT_LOC%7D</value>
		</variable>
	</variableList>
</projectDescription>
